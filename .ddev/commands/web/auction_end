#!/bin/bash
#ddev-generated
## Description: Auction end script
## Usage: auction_end
## Example: "ddev auction_end"

set -e

SCRIPT_PATH="scripts/auction_end.sh"
LOG_PATH="/var/www/html/auctionend.log"

echo "Running auction end script: $SCRIPT_PATH"

if [ -f "$SCRIPT_PATH" ]; then
  # Execute the script and redirect stdout/stderr to tee, saving to log file
  bash "$SCRIPT_PATH" 2>&1 | tee "$LOG_PATH"
else
  echo "Error: Auction end script not found at $SCRIPT_PATH" >&2
  exit 1
fi

echo "Auction end script finished. Log available at auctionend.log in project root (mounted from $LOG_PATH inside container)." 