<?php

/**
 * @file
 * Install functions for the baguettebox module.
 */

/**
 * Implements hook_requirements().
 */
function baguettebox_requirements($phase) {
  $requirements = [];

  if ($phase == 'runtime') {

    if (!file_exists('libraries/baguettebox.js/dist/baguetteBox.min.js')) {
      $requirements['baguettebox_library'] = [
        'title' => t('BaguetteBox library'),
        'value' => t('Not found'),
        'severity' => REQUIREMENT_ERROR,
        'description' => t(
          'You need to download the <a href=":library_url">baguetteBox library</a> and extract the archive to the <em>libraries/baguettebox.js</em> directory on your server.',
          [':library_url' => 'https://github.com/feimosi/baguetteBox.js']
        ),
      ];
    }

  }

  return $requirements;
}
