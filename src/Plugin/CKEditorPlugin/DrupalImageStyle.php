<?php

namespace Dr<PERSON>al\inline_responsive_images\Plugin\CKEditorPlugin;

use <PERSON><PERSON><PERSON>\Component\Plugin\PluginBase;
use <PERSON><PERSON><PERSON>\editor\Entity\Editor;
use <PERSON><PERSON><PERSON>\ckeditor\CKEditorPluginInterface;
use <PERSON><PERSON><PERSON>\ckeditor\CKEditorPluginContextualInterface;

/**
 * Defines the "drupalimagestyle" CKE4 plugin.
 *
 * @CKEditorPlugin(
 *   id = "drupalimagestyle",
 *   label = @Translation("Drupal image style"),
 *   module = "inline_responsive_images"
 * )
 */
class DrupalImageStyle extends PluginBase implements CKEditorPluginInterface, CKEditorPluginContextualInterface {

  /**
   * {@inheritdoc}
   */
  public function isInternal() {
    return FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function getDependencies(Editor $editor) {
    return [];
  }

  /**
   * {@inheritdoc}
   */
  public function getLibraries(Editor $editor) {
    return [];
  }

  /**
   * {@inheritdoc}
   */
  public function getFile() {
    return \Drupal::service('extension.list.module')->getPath('inline_responsive_images') . '/js/plugins/drupalimagestyle/plugin.js';
  }

  /**
   * {@inheritdoc}
   */
  public function getConfig(Editor $editor) {
    return [];
  }

  /**
   * {@inheritdoc}
   */
  public function isEnabled(Editor $editor) {
    if (!$editor->hasAssociatedFilterFormat()) {
      return FALSE;
    }

    // Automatically enable this plugin if the text format associated with this
    // text editor uses the filter_imagestyle filter and the DrupalImage button
    // is enabled.
    $format = $editor->getFilterFormat();
    if ($format->filters('filter_imagestyle')->status) {
      $enabled = FALSE;
      $settings = $editor->getSettings();
      foreach ($settings['toolbar']['rows'] as $row) {
        foreach ($row as $group) {
          foreach ($group['items'] as $button) {
            if ($button === 'DrupalImage') {
              $enabled = TRUE;
            }
          }
        }
      }
      return $enabled;
    }

    return FALSE;
  }

}
