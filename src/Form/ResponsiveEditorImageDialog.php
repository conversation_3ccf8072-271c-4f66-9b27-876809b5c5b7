<?php

namespace Drupal\inline_responsive_images\Form;

use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\Core\Ajax\AjaxResponse;
use <PERSON><PERSON>al\Core\Ajax\HtmlCommand;
use <PERSON><PERSON><PERSON>\editor\Ajax\EditorDialogSave;
use <PERSON><PERSON>al\Core\Ajax\CloseModalDialogCommand;
use <PERSON><PERSON>al\editor\Form\EditorImageDialog;

/**
 * Provides an image dialog for CKE4 text editors.
 */
class ResponsiveEditorImageDialog extends EditorImageDialog {

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $response = new AjaxResponse();

    // Convert any uploaded files from the FID values to data-entity-uuid
    // attributes and set data-entity-type to 'file'.
    $fid = $form_state->getValue(['fid', 0]);
    if (!empty($fid)) {
      $file = $this->fileStorage->load($fid);
      $file_url = \Drupal::service('file_url_generator')->generateAbsoluteString($file->getFileUri());
      // Transform absolute image URLs to relative image URLs: prevent problems
      // on multisite set-ups and prevent mixed content errors.
      $file_url = \Drupal::service('file_url_generator')->transformRelative($file_url);
      $form_state->setValue(['attributes', 'src'], $file_url);
      $form_state->setValue(['attributes', 'data-entity-uuid'], $file->uuid());
      $form_state->setValue(['attributes', 'data-entity-type'], 'file');
    }

    // When the alt attribute is set to two double quotes, transform it to the
    // empty string: two double quotes signify "empty alt attribute". See above.
    if (trim($form_state->getValue(['attributes', 'alt'])) === '""') {
      $form_state->setValue(['attributes', 'alt'], '');
    }

    if ($form_state->getErrors()) {
      unset($form['#prefix'], $form['#suffix']);
      $form['status_messages'] = [
        '#type' => 'status_messages',
        '#weight' => -10,
      ];
      $response->addCommand(new HtmlCommand('#editor-image-dialog-form', $form));
    }
    else {
      $response->addCommand(new EditorDialogSave($form_state->getValues()));
      $response->addCommand(new CloseModalDialogCommand());
    }

    return $response;
  }

}
