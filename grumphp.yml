parameters:
  grumphp.run_on_paths: ['web/modules/custom', 'web/themes/custom/mb']
grumphp:
  stop_on_failure: true
  process_timeout: 300
  git_hook_variables:
    EXEC_GRUMPHP_COMMAND: lando php
  ascii:
    failed: ~
    succeeded: ~
  tasks:
    phpstan:
      configuration: phpstan.neon
      level: ~
      triggered_by: ['php', 'module', 'inc', 'install', 'theme']
      memory_limit: "-1"
      use_grumphp_paths: true
    php_compatibility:
      run_on: '%grumphp.run_on_paths%'
      testVersion: '8.3'
    phpcs:
      standard: ['phpcs.xml']
      whitelist_patterns:
        - web/modules/custom
        - web/themes/custom
      triggered_by: ['php', 'module', 'inc', 'install']
    yamllint: ~
    jsonlint: ~
    xmllint: ~
  extensions:
    - Wunderio\GrumPHP\Task\PhpCompatibility\PhpCompatibilityExtensionLoader
