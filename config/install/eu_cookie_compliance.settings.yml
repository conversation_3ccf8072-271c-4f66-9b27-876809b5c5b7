uuid: ''
cookie_lifetime: 100
containing_element: 'body'
domain: ''
domains_option: 1
domains_list: ''
eu_countries: { }
eu_only: false
eu_only_js: false
exclude_paths: "/admin\n/admin/*\n/batch\n/node/add*\n/node/*/*\n/user/*/*"
exclude_admin_theme: false
langcode: en
popup_agreed:
  value: ''
  format: 'restricted_html'
popup_agree_button_message: ''
popup_agreed_enabled: false
popup_bg_hex: '0779bf'
popup_clicking_confirmation: true
popup_scrolling_confirmation: false
popup_delay: 1000
show_more_info: true
popup_more_info_button_message: ''
popup_enabled: true
popup_find_more_button_message: ''
popup_height: 0
popup_hide_agreed: false
popup_hide_button_message: ''
popup_info:
  value: ''
  format: 'restricted_html'
mobile_popup_info:
  value: ''
  format: 'restricted_html'
popup_info_template: 'new'
mobile_breakpoint: 768
popup_link: ''
popup_link_new_window: true
popup_position: false
fixed_top_position: true
popup_text_hex: 'ffffff'
popup_width: '100%'
use_bare_css: false
use_olivero_css: true
disagree_do_not_show_popup: false
reload_page: false
cookie_name: ''
exclude_uid_1: false
better_support_for_screen_readers: false
use_mobile_message: false
method: 'opt_in'
disagree_button_label: ''
disabled_javascripts: ''
automatic_cookies_removal: true
allowed_cookies: ''
consent_storage_method: 'do_not_store'
withdraw_message:
  value: ''
  format: 'restricted_html'
withdraw_action_button_label: ''
withdraw_tab_button_label: ''
withdraw_enabled: false
enable_save_preferences_button: true
save_preferences_button_label: ''
accept_all_categories_button_label: ''
withdraw_button_on_info_popup: false
domain_all_sites: false
settings_tab_enabled: false
reload_options: 0
reload_routes_list: ''
cookie_policy_version: '1.0.0'
cookie_value_disagreed: '0'
cookie_value_agreed_show_thank_you: '1'
cookie_value_agreed: '2'
accessibility_focus: false
close_button_action: 'close_banner'
reject_button_label: ''
reject_button_enabled: false
close_button_enabled: false
