diff --git a/src/Entity/MessageTemplate.php b/src/Entity/MessageTemplate.php
index 7dc9289..782f034 100644
--- a/src/Entity/MessageTemplate.php
+++ b/src/Entity/MessageTemplate.php
@@ -265,7 +265,7 @@ class MessageTemplate extends ConfigEntityBundleBase implements MessageTemplateI
 
       if ($langcode == Language::LANGCODE_NOT_SPECIFIED) {
         // Get the current language code when not specified.
-        $langcode = $language_manager->getCurrentLanguage()->getId();
+        $langcode = $language_manager->getDefaultLanguage()->getId();
       }
 
       if ($this->langcode !== $langcode) {
