field.formatter.settings.degree_360:
  type: mapping
  label: "Degree 360 settings"
  mapping:
    width:
      type: string
      label: "Width"
    height:
      type: string
      label: "Height"
    loading_msg:
      type: string
      label: "Loading msg"
    navbar_enable:
      type: boolean
      label: "Show Navbar"
    navbar_backgroundColor:
      type: string
      label: "Navigation Bar Background Color"
    navbar_buttonsColor:
      type: string
      label: "Buttons forground color"
    navbar_buttonsBackgroundColor:
      type: string
      label: "Button background color"
    navbar_activeButtonsBackgroundColor:
      type: string
      label: "Active button background color"
    navbar_buttonsHeight:
      type: integer
      label: "Buttons height"
    navbar_autorotateThickness:
      type: integer
      label: "Autorotate icon thickness"
    navbar_zoomRangeWidth:
      type: integer
      label: "Zoom range bar width"
    navbar_zoomRangeThickness:
      type: integer
      label: "Zoom range bar thickness"
    navbar_zoomRangeDisk:
      type: integer
      label: "Zoom range disk diameter"
    navbar_fullscreenRatio:
      type: integer
      label: "Fullscreen icon ratio"
    navbar_fullscreenThickness:
      type: integer
      label: "Fullscreen icon thickness"
