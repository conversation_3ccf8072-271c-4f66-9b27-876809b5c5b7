_core:
  default_config_hash: LzgCCLHNdnKpzaik9_JdBqT1OJeHgzmR5ba__liKWvA
langcode: en
name: google_api_client_google_api_services
label: 'Google Api Client google api services'
google_api_client_google_api_services:
  acmedns: ACMEDNS
  aiplatformnotebooks: AIPlatformNotebooks
  abusiveexperiencereport: 'Abusive Experience Report API'
  acceleratedmobilepageurl: 'Accelerated Mobile Pages (AMP) URL API'
  accessapproval: 'Access Approval API'
  accesscontextmanager: 'Access Context Manager API'
  adexchangebuyer: 'Ad Exchange Buyer API'
  adexchangebuyerii: AdExchangeBuyerII
  adexperiencereport: 'Ad Experience Report API'
  admob: AdMob
  adsensehost: 'AdSense Host API'
  adsense: 'AdSense Management API'
  advisorynotifications: Advisorynotifications
  alertcenter: 'G Suite Alert Center API'
  analytics: 'Google Analytics API'
  analyticsdata: AnalyticsData
  analyticshub: AnalyticsHub
  analyticsreporting: 'Analytics Reporting API'
  androidenterprise: 'Google Play EMM API'
  androidmanagement: 'Android Management API'
  androidprovisioningpartner: AndroidProvisioningPartner
  androidpublisher: 'Google Play Developer API'
  apikeysservice: ApiKeysService
  apigateway: Apigateway
  apigee: Apigee
  apigeeregistry: ApigeeRegistry
  appengine: 'App Engine Admin API'
  area120tables: Area120Tables
  artifactregistry: ArtifactRegistry
  assuredworkloads: Assuredworkloads
  authorizedbuyersmarketplace: AuthorizedBuyersMarketplace
  backupforgke: BackupforGKE
  baremetalsolution: Baremetalsolution
  batch: Batch
  beyondcorp: BeyondCorp
  bigqueryconnectionservice: BigQueryConnectionService
  bigquerydatatransfer: 'BigQuery Data Transfer API'
  bigqueryreservation: 'BigQuery Reservation API'
  bigquery: 'BigQuery API'
  bigtableadmin: 'Cloud Bigtable Admin API'
  binaryauthorization: 'Binary Authorization API'
  blogger: 'Blogger API'
  books: 'Books API'
  businessprofileperformance: BusinessProfilePerformance
  ccaiplatform: CCAIPlatform
  calendar: 'Calendar API'
  certificateauthorityservice: CertificateAuthorityService
  certificatemanager: CertificateManager
  chromemanagement: ChromeManagement
  chromepolicy: ChromePolicy
  chromeuxreport: ChromeUXReport
  civicinfo: 'Google Civic Information API'
  classroom: 'Google Classroom API'
  cloudasset: 'Cloud Asset API'
  cloudbillingbudget: CloudBillingBudget
  cloudbuild: 'Cloud Build API'
  cloudcomposer: CloudComposer
  clouddataplex: CloudDataplex
  clouddebugger: 'Stackdriver Debugger API'
  clouddeploy: CloudDeploy
  clouddomains: CloudDomains
  cloudfilestore: CloudFilestore
  cloudfunctions: 'Cloud Functions API'
  cloudhealthcare: CloudHealthcare
  cloudiap: CloudIAP
  cloudidentity: 'Cloud Identity API'
  cloudiot: 'Cloud IoT API'
  cloudkms: 'Cloud Key Management Service (KMS) API'
  cloudlifesciences: CloudLifeSciences
  cloudmachinelearningengine: CloudMachineLearningEngine
  cloudmemorystoreformemcached: CloudMemorystoreforMemcached
  cloudnaturallanguage: CloudNaturalLanguage
  cloudoslogin: CloudOSLogin
  cloudprofiler: 'Stackdriver Profiler API'
  cloudredis: CloudRedis
  cloudresourcemanager: 'Cloud Resource Manager API'
  cloudretail: CloudRetail
  cloudrun: CloudRun
  cloudruntimeconfig: CloudRuntimeConfig
  cloudscheduler: 'Cloud Scheduler API'
  cloudsearch: 'Cloud Search API'
  cloudsecuritytoken: CloudSecurityToken
  cloudshell: 'Cloud Shell API'
  cloudsourcerepositories: CloudSourceRepositories
  cloudsupport: CloudSupport
  cloudtalentsolution: CloudTalentSolution
  cloudtasks: 'Cloud Tasks API'
  cloudtrace: 'Stackdriver Trace API'
  cloudvideointelligence: CloudVideoIntelligence
  cloudworkstations: CloudWorkstations
  cloudbilling: 'Cloud Billing API'
  cloudchannel: Cloudchannel
  clouderrorreporting: 'Stackdriver Error Reporting API'
  compute: 'Compute Engine API'
  connectors: Connectors
  contactcenterinsights: Contactcenterinsights
  container: 'Kubernetes Engine API'
  containeranalysis: 'Container Analysis API'
  contentwarehouse: Contentwarehouse
  customsearchapi: CustomSearchAPI
  dlp: 'Cloud Data Loss Prevention (DLP) API'
  datacatalog: DataCatalog
  datafusion: 'Cloud Data Fusion API'
  datalabeling: DataLabeling
  datatransfer: DataTransfer
  databasemigrationservice: DatabaseMigrationService
  dataflow: 'Dataflow API'
  dataform: Dataform
  datalineage: Datalineage
  datapipelines: Datapipelines
  dataproc: 'Cloud Dataproc API'
  dataprocmetastore: DataprocMetastore
  datastore: 'Cloud Datastore API'
  datastream: Datastream
  deploymentmanager: 'Google Cloud Deployment Manager API'
  dfareporting: 'DCM/DFA Reporting And Trafficking API'
  dialogflow: 'Dialogflow API'
  digitalassetlinks: 'Digital Asset Links API'
  directory: Directory
  discoveryengine: DiscoveryEngine
  displayvideo: DisplayVideo
  dns: 'Google Cloud DNS API'
  docs: 'Google Docs API'
  document: Document
  domainsrdap: 'Domains RDAP API'
  doubleclickbidmanager: 'DoubleClick Bid Manager API'
  doubleclicksearch: 'Search Ads 360 API'
  drive: 'Drive API'
  driveactivity: 'Drive Activity API'
  drivelabels: DriveLabels
  essentialcontacts: Essentialcontacts
  eventarc: Eventarc
  factchecktools: 'Fact Check Tools API'
  fcmdata: Fcmdata
  firebaseappdistribution: FirebaseAppDistribution
  firebasecloudmessaging: FirebaseCloudMessaging
  firebasedynamiclinks: 'Firebase Dynamic Links API'
  firebasehosting: 'Firebase Hosting API'
  firebaseml: FirebaseML
  firebasemanagement: FirebaseManagement
  firebaserealtimedatabase: FirebaseRealtimeDatabase
  firebaserules: 'Firebase Rules API'
  firebaseappcheck: Firebaseappcheck
  firebasestorage: Firebasestorage
  firestore: 'Cloud Firestore API'
  fitness: Fitness
  forms: Forms
  gkehub: GKEHub
  gameservices: GameServices
  games: 'Google Play Game Services API'
  gamesconfiguration: GamesConfiguration
  gamesmanagement: GamesManagement
  genomics: 'Genomics API'
  gmail: 'Gmail API'
  googleanalyticsadmin: GoogleAnalyticsAdmin
  groupsmigration: 'Groups Migration API'
  groupssettings: 'Groups Settings API'
  hangoutschat: HangoutsChat
  homegraphservice: HomeGraphService
  iamcredentials: 'IAM Service Account Credentials API'
  ids: IDS
  iam: 'Identity and Access Management (IAM) API'
  ideahub: Ideahub
  identitytoolkit: 'Google Identity Toolkit API'
  indexing: 'Indexing API'
  integrations: Integrations
  keep: Keep
  kgsearch: 'Knowledge Graph Search API'
  kmsinventory: Kmsinventory
  libraryagent: 'Library Agent API'
  licensing: 'Enterprise License Manager API'
  localservices: Localservices
  logging: 'Stackdriver Logging API'
  managedserviceformicrosoftactivedirectoryconsumerapi: ManagedServiceforMicrosoftActiveDirectoryConsumerAPI
  manufacturercenter: ManufacturerCenter
  migrationcenterapi: MigrationCenterAPI
  monitoring: 'Stackdriver Monitoring API'
  mybusinessaccountmanagement: MyBusinessAccountManagement
  mybusinessbusinesscalls: MyBusinessBusinessCalls
  mybusinessbusinessinformation: MyBusinessBusinessInformation
  mybusinesslodging: MyBusinessLodging
  mybusinessnotificationsettings: MyBusinessNotificationSettings
  mybusinessplaceactions: MyBusinessPlaceActions
  mybusinessqa: MyBusinessQA
  mybusinessverifications: MyBusinessVerifications
  networkmanagement: NetworkManagement
  networksecurity: NetworkSecurity
  networkservices: NetworkServices
  networkconnectivity: Networkconnectivity
  osconfig: 'Cloud OS Config API'
  oauth2: 'Google OAuth2 API'
  ondemandscanning: OnDemandScanning
  orgpolicyapi: OrgPolicyAPI
  pagespeedinsights: PagespeedInsights
  paymentsresellersubscription: PaymentsResellerSubscription
  peopleservice: PeopleService
  playintegrity: PlayIntegrity
  playablelocations: PlayableLocations
  playcustomapp: 'Google Play Custom App Publishing API'
  playdeveloperreporting: Playdeveloperreporting
  policyanalyzer: PolicyAnalyzer
  policysimulator: PolicySimulator
  policytroubleshooter: PolicyTroubleshooter
  polyservice: PolyService
  postmastertools: PostmasterTools
  publiccertificateauthority: PublicCertificateAuthority
  pubsub: 'Cloud Pub/Sub API'
  pubsublite: PubsubLite
  realtimebidding: RealTimeBidding
  recaptchaenterprise: RecaptchaEnterprise
  recommendationsai: RecommendationsAI
  recommender: Recommender
  remotebuildexecution: 'Remote Build Execution API'
  reports: Reports
  reseller: 'Enterprise Apps Reseller API'
  resourcesettings: ResourceSettings
  sa360: SA360
  sasportaltesting: SASPortalTesting
  sqladmin: 'Cloud SQL Admin API'
  safebrowsing: 'Safe Browsing API'
  sasportal: Sasportal
  script: 'Apps Script API'
  searchconsole: 'Google Search Console URL Testing Tools API'
  secretmanager: SecretManager
  securitycommandcenter: SecurityCommandCenter
  semantictile: SemanticTile
  serverlessvpcaccess: ServerlessVPCAccess
  serviceconsumermanagement: 'Service Consumer Management API'
  servicecontrol: 'Service Control API'
  servicedirectory: ServiceDirectory
  servicemanagement: 'Service Management API'
  servicenetworking: 'Service Networking API'
  serviceusage: 'Service Usage API'
  sheets: 'Google Sheets API'
  shoppingcontent: ShoppingContent
  siteverification: SiteVerification
  slides: 'Google Slides API'
  smartdevicemanagement: SmartDeviceManagement
  spanner: 'Cloud Spanner API'
  speech: 'Cloud Speech-to-Text API'
  storage: 'Cloud Storage JSON API'
  storagetransfer: 'Storage Transfer API'
  streetviewpublish: 'Street View Publish API'
  subscriptionlinking: SubscriptionLinking
  tpu: 'Cloud TPU API'
  tagmanager: 'Tag Manager API'
  tasks: 'Tasks API'
  testing: 'Cloud Testing API'
  texttospeech: 'Cloud Text-to-Speech API'
  toolresults: 'Cloud Tool Results API'
  trafficdirectorservice: TrafficDirectorService
  transcoder: Transcoder
  translate: 'Cloud Translation API'
  travelimpactmodel: TravelImpactModel
  vmmigrationservice: VMMigrationService
  vault: 'G Suite Vault API'
  verifiedaccess: 'Chrome Verified Access API'
  versionhistory: VersionHistory
  vision: 'Cloud Vision API'
  webrisk: WebRisk
  webfonts: 'Google Fonts Developer API'
  webmasters: 'Search Console API'
  workflowexecutions: WorkflowExecutions
  workflows: Workflows
  workloadmanager: WorkloadManager
  youtube: 'YouTube Data API'
  youtubeanalytics: YouTubeAnalytics
  youtubereporting: 'YouTube Reporting API�'
