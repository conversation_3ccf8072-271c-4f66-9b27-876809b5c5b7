uuid: 3e4da01e-fa13-41db-9d3a-ee0adcfe26dc
langcode: en
status: true
dependencies:
  module:
    - auctions
    - options
id: auction.field_step
field_name: field_step
entity_type: auction
type: list_float
settings:
  allowed_values:
    -
      value: 100.0
      label: '100'
    -
      value: 50.0
      label: '50'
    -
      value: 10.0
      label: '10'
    -
      value: 5.0
      label: '5'
    -
      value: 1.0
      label: '1'
    -
      value: 0.1
      label: '0.10'
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
