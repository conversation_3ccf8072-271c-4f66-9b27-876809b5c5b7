uuid: 8036ad0f-c3e0-4be7-a128-afc0ce96a8e4
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.storage.auction.field_extra_emails
id: auction.stumpage.field_extra_emails
field_name: field_extra_emails
entity_type: auction
bundle: stumpage
label: 'Extra emails'
description: 'Here you can add additional e-mail addresses that will receive notifications when auction has been activated and finished.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: email
