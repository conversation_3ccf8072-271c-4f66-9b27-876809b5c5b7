uuid: 44c43a0c-ce51-416a-82ef-b68a51437433
langcode: en
status: true
dependencies:
  config:
    - field.field.node.page.field_content
    - field.field.node.page.field_hero
    - field.field.node.page.field_hide_label
    - field.field.node.page.field_ingress
    - field.field.node.page.field_meta
    - node.type.page
  module:
    - field_group
    - metatag
    - paragraphs_asymmetric_translation_widgets
    - path
    - text
third_party_settings:
  field_group:
    group_intro:
      children:
        - field_ingress
      label: Intro
      region: content
      parent_name: ''
      weight: 5
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
id: node.page.default
targetEntityType: node
bundle: page
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  field_content:
    type: paragraphs_classic_asymmetric
    weight: 6
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
    third_party_settings: {  }
  field_hero:
    type: paragraphs_classic_asymmetric
    weight: 4
    region: content
    settings:
      title: Hero
      title_plural: Heroes
      edit_mode: closed
      add_mode: dropdown
      form_display_mode: hero
      default_paragraph_type: _none
    third_party_settings: {  }
  field_hide_label:
    type: boolean_checkbox
    weight: 1
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_ingress:
    type: text_textarea
    weight: 27
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_meta:
    type: metatag_firehose
    weight: 13
    region: content
    settings:
      sidebar: true
      use_details: true
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 3
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  simple_sitemap:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 8
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 12
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  promote: true
  sticky: true
