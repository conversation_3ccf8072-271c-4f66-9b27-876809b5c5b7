uuid: 465db507-03ad-47bb-9cae-ed9fefcf71ac
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.storage.auction.field_route_and_strorage
  module:
    - options
    - require_on_publish
third_party_settings:
  require_on_publish:
    require_on_publish: true
id: auction.stumpage.field_route_and_strorage
field_name: field_route_and_strorage
entity_type: auction
bundle: stumpage
label: 'Coordination of access routes and storage'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
