uuid: bad71419-7d91-465d-9788-1df8b0e697dc
langcode: en
status: true
dependencies: {  }
name: news_teaser
label: 'News teaser'
effects:
  2c23935f-ecbf-4964-980d-c5e4b2bf15d6:
    uuid: 2c23935f-ecbf-4964-980d-c5e4b2bf15d6
    id: image_scale_and_crop
    weight: 1
    data:
      width: 500
      height: 330
      anchor: center-center
  bdf62446-7bbc-4579-a8f8-5832d76bcf67:
    uuid: bdf62446-7bbc-4579-a8f8-5832d76bcf67
    id: image_convert
    weight: 2
    data:
      extension: webp
pipeline: __default__
