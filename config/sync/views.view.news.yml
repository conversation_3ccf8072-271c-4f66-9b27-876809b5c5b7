uuid: 58f8fbc2-2b45-4eac-9a51-2e4386db5238
langcode: en
status: true
dependencies:
  config:
    - system.menu.main
  module:
    - better_exposed_filters
    - elasticsearch_handler
    - inqube
    - views_infinite_scroll
id: news
label: News
module: views
description: 'Elasticsearch implementation for news listing'
tag: ''
base_table: elasticsearch_result
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        rendered_entity:
          id: rendered_entity
          table: elasticsearch_result
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: elasticsearch_rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode:
            -
              'node:news': related
          set_result_on_entity: 0
      pager:
        type: infinite_scroll
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 1
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          views_infinite_scroll:
            button_text: 'Load More'
            automatically_load_content: true
            initially_load_all_pages: false
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: full
          bef:
            general:
              autosubmit: true
              autosubmit_exclude_textfield: true
              autosubmit_textfield_delay: 500
              autosubmit_textfield_minimum_length: 3
              autosubmit_hide: true
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
            filter:
              keyword:
                plugin_id: default
                advanced:
                  placeholder_text: ''
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              filter_keywords:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: true
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'No results found'
            format: full
          tokenize: false
      sorts:
        created:
          id: created
          table: elasticsearch_result
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments: {  }
      filters:
        keyword:
          id: keyword
          table: elasticsearch_result
          field: keyword
          relationship: none
          group_type: group
          admin_label: Search
          plugin_id: string
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: keyword_op
            label: ''
            description: ''
            use_operator: false
            operator: keyword_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: keyword
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            placeholder: Search
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_keywords:
          id: filter_keywords
          table: elasticsearch_result
          field: filter_keywords
          relationship: none
          group_type: group
          admin_label: 'Keyword tags'
          plugin_id: term_keywords
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: filter_keywords_op
            label: ''
            description: ''
            use_operator: false
            operator: filter_keywords_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_keywords
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_builder: news_search
          entity_relationship:
            entity_type_key: _source.entity
            entity_id_key: _source.id
      relationships: {  }
      css_class: no-sidebar
      use_ajax: true
      header:
        result:
          id: result
          table: views
          field: result
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: result
          empty: true
          content: '@total search results'
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
      tags:
        - 'node_list:news'
  page:
    id: page
    display_title: 'All news page'
    display_plugin: page
    position: 2
    display_options:
      title: News
      defaults:
        title: false
      display_description: ''
      display_extenders:
        metatag_display_extender:
          metatags:
            title: News
            description: 'Stay updated with the latest news on stumpage auctions, forestry market trends, and industry insights. Your go-to source for auction announcements, timber prices, and forestry regulations.'
          tokenize: false
        ajax_history:
          enable_history: true
      path: news
      menu:
        type: normal
        title: News
        description: ''
        weight: 0
        expanded: false
        menu_name: main
        parent: ''
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
      tags:
        - 'node_list:news'
  topical_news:
    id: topical_news
    display_title: 'Topical news'
    display_plugin: embed
    position: 1
    display_options:
      fields:
        rendered_entity:
          id: rendered_entity
          table: elasticsearch_result
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: elasticsearch_rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode:
            -
              'node:news': topic
          set_result_on_entity: 0
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 4
      cache:
        type: none
        options: {  }
      empty: {  }
      filters:
        filter_keywords:
          id: filter_keywords
          table: elasticsearch_result
          field: filter_keywords
          relationship: none
          group_type: group
          admin_label: 'Keyword tags'
          plugin_id: term_keywords
          operator: 'not empty'
          value: {  }
          group: 1
          exposed: false
          expose:
            operator_id: filter_keywords_op
            label: ''
            description: ''
            use_operator: false
            operator: filter_keywords_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_keywords
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        empty: false
        cache: false
        use_ajax: false
        pager: false
        fields: false
        filters: false
        filter_groups: false
        header: false
      use_ajax: false
      display_description: ''
      header: {  }
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history:
          enable_history: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
      tags:
        - 'node_list:news'
