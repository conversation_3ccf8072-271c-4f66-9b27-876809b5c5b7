uuid: d3502406-cf95-4a88-acdf-dc096ce35b8d
langcode: en
status: true
dependencies:
  module:
    - better_exposed_filters
    - elasticsearch_handler
    - inqube
id: all_auctions
label: 'All auctions'
module: views
description: ''
tag: ''
base_table: elasticsearch_result
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'All auctions'
      fields:
        rendered_entity:
          id: rendered_entity
          table: elasticsearch_result
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: elasticsearch_rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode:
            -
              'auction:stumpage': teaser
          set_result_on_entity: 0
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 15
          total_pages: null
          id: 0
          tags:
            next: Nākošā
            previous: Atpakaļ
            first: Pirmā
            last: Pēdējā
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 5
      exposed_form:
        type: bef
        options:
          submit_button: Search
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Order by'
          expose_sort_order: true
          sort_asc_label: ↑
          sort_desc_label: ↓
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic
          bef:
            general:
              autosubmit: true
              autosubmit_exclude_textfield: true
              autosubmit_textfield_delay: 500
              autosubmit_textfield_minimum_length: 3
              autosubmit_hide: true
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
            sort:
              plugin_id: bef
              advanced:
                combine: true
                combine_rewrite: ''
                reset: false
                reset_label: ''
                collapsible: false
                collapsible_label: 'Sort options'
                is_secondary: false
            filter:
              filter_administrative_areas_term:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              filter_price_group:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              filter_forest_volume:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              filter_auction_status:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              filter_from:
                plugin_id: default
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              filter_to:
                plugin_id: default
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
      access:
        type: none
        options: {  }
      cache:
        type: none
        options: {  }
      empty: {  }
      sorts:
        end_time:
          id: end_time
          table: elasticsearch_result
          field: end_time
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: 'End time'
            field_identifier: end_time
          exposed: true
        current_price:
          id: current_price
          table: elasticsearch_result
          field: current_price
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: Price
            field_identifier: current_price
          exposed: true
      arguments: {  }
      filters:
        filter_administrative_areas_term:
          id: filter_administrative_areas_term
          table: elasticsearch_result
          field: filter_administrative_areas_term
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_administrative_areas_term
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: filter_administrative_areas_term_op
            label: Region
            description: ''
            use_operator: false
            operator: filter_administrative_areas_term_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_administrative_areas_term
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: 0
          type: select
          limit: true
          vid: administrative_areas
          hierarchy: 0
          error_message: 0
        filter_price_group:
          id: filter_price_group
          table: elasticsearch_result
          field: filter_price_group
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_price_group
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: filter_price_group_op
            label: 'Price group'
            description: ''
            use_operator: false
            operator: filter_price_group_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_price_group
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_forest_volume:
          id: filter_forest_volume
          table: elasticsearch_result
          field: filter_forest_volume
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_forest_volume
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: filter_forest_volume_op
            label: 'Forest volume'
            description: ''
            use_operator: false
            operator: filter_forest_volume_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_forest_volume
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_auction_status:
          id: filter_auction_status
          table: elasticsearch_result
          field: filter_auction_status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_status
          operator: in
          value:
            active: active
            canceled: canceled
            finished: finished
          group: 1
          exposed: true
          expose:
            operator_id: filter_auction_status_op
            label: Status
            description: ''
            use_operator: false
            operator: filter_auction_status_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_auction_status
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: true
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_from:
          id: filter_from
          table: elasticsearch_result
          field: filter_from
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: elastic_date
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: filter_from_op
            label: From
            description: ''
            use_operator: false
            operator: filter_from_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_from
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_to:
          id: filter_to
          table: elasticsearch_result
          field: filter_to
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: elastic_date
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: filter_to_op
            label: To
            description: ''
            use_operator: false
            operator: filter_to_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_to
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_builder: auctions
          entity_relationship:
            entity_type_key: _source.entity
            entity_id_key: _source.id
      relationships: {  }
      use_ajax: true
      header:
        auction_summary:
          id: auction_summary
          table: elasticsearch_result
          field: auction_summary
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: auction_summary
          empty: true
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
      tags: {  }
  search:
    id: search
    display_title: Izsoles
    display_plugin: page
    position: 1
    display_options:
      display_description: ''
      exposed_block: false
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history:
          enable_history: true
      path: auction
      menu:
        type: none
        title: 'Visas izsoles'
        description: ''
        weight: -46
        expanded: false
        menu_name: main
        parent: ''
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
      tags: {  }
