uuid: fd2d1437-bb3c-4912-9238-2afb6a61e382
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_blocks
    - paragraphs.paragraphs_type.image
    - paragraphs.paragraphs_type.logo_slider
  module:
    - entity_reference_revisions
id: paragraph.logo_slider.field_blocks
field_name: field_blocks
entity_type: paragraph
bundle: logo_slider
label: Logo
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      image: image
    negate: 0
    target_bundles_drag_drop:
      accordion_item:
        weight: 17
        enabled: false
      banner:
        weight: 18
        enabled: false
      calculator:
        weight: 19
        enabled: false
      contact_us:
        weight: 20
        enabled: false
      icon_card:
        weight: 21
        enabled: false
      icon_text:
        weight: 22
        enabled: false
      image:
        weight: 24
        enabled: true
      logo_slider:
        weight: 23
        enabled: false
      person:
        weight: 24
        enabled: false
      quote:
        weight: 25
        enabled: false
      quotes:
        weight: 26
        enabled: false
      slide:
        weight: 27
        enabled: false
      slideshow:
        weight: 28
        enabled: false
      species:
        weight: 29
        enabled: false
      text:
        weight: 30
        enabled: false
      title:
        weight: 31
        enabled: false
      view:
        weight: 32
        enabled: false
field_type: entity_reference_revisions
