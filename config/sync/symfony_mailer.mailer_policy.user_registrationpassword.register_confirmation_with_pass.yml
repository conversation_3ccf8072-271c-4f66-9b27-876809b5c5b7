uuid: 9504cc30-2f86-43df-b921-151a5ea100a8
langcode: en
status: true
dependencies: {  }
id: user_registrationpassword.register_confirmation_with_pass
configuration:
  email_subject:
    value: 'Account details for [user:display-name] at [site:name]'
  email_body:
    content:
      value: |-
        <p>[user:display-name],</p>
        <p>Thank you for registering at <a href="[site:url]">[site:name]</a>.
        You may now use this link to <a href="[user:registrationpassword-url]">log in</a>. It can be used only once and you will be able to <a href="[site:login-url]">log in</a> in the future using:</p>
        <ul>
          <li>username: [user:account-name]</li>
          <li>password: Your password</li>
        </ul>
      format: email_html
