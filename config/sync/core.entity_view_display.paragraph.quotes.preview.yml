uuid: 5218b78c-9ec1-44b2-a412-fef6120e13b9
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.paragraph.preview
    - field.field.paragraph.quotes.field_blocks
    - paragraphs.paragraphs_type.quotes
  module:
    - slick_paragraphs
id: paragraph.quotes.preview
targetEntityType: paragraph
bundle: quotes
mode: preview
content:
  field_blocks:
    type: slick_paragraphs_vanilla
    label: hidden
    settings:
      optionset: default
      view_mode: default
      cache: 0
      use_theme_field: false
      skin: ''
      overridables:
        arrows: arrows
        draggable: draggable
        infinite: infinite
        autoplay: '0'
        dots: '0'
        mouseWheel: '0'
        randomize: '0'
        variableWidth: '0'
      override: true
      skin_arrows: ''
      skin_dots: ''
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
