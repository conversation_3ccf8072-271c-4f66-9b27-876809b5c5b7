uuid: 465f3287-aa60-4e63-87dc-1d8fc8df5bf8
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.species.field_cubic_meters
    - field.field.paragraph.species.field_specie
    - paragraphs.paragraphs_type.species
  module:
    - options
id: paragraph.species.default
targetEntityType: paragraph
bundle: species
mode: default
content:
  field_cubic_meters:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 1
    region: content
  field_specie:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
