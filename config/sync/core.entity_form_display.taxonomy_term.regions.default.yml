uuid: 7f8fc0bb-f0aa-4cf1-a594-fd3b64cb4f70
langcode: en
status: true
dependencies:
  config:
    - field.field.taxonomy_term.regions.field_final
    - field.field.taxonomy_term.regions.field_identifier
    - field.field.taxonomy_term.regions.field_map
    - field.field.taxonomy_term.regions.field_search_inflection
    - taxonomy.vocabulary.regions
  module:
    - leaflet
id: taxonomy_term.regions.default
targetEntityType: taxonomy_term
bundle: regions
mode: default
content:
  field_final:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_identifier:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_map:
    type: leaflet_widget_default
    weight: 4
    region: content
    settings:
      map:
        leaflet_map: 'Auction widget'
        height: 400
        auto_center: 1
        map_position:
          force: false
          center:
            lat: 0.0
            lon: 0.0
          zoomControlPosition: topleft
          zoom: 5
          minZoom: 1
          maxZoom: 18
          zoomFiner: 0
        scroll_zoom_enabled: 1
      input:
        show: true
        readonly: false
      toolbar:
        position: topright
        marker: defaultMarker
        drawPolyline: true
        drawRectangle: true
        drawPolygon: true
        drawCircle: false
        drawText: false
        editMode: true
        dragMode: true
        cutPolygon: false
        removalMode: true
        rotateMode: false
      reset_map:
        control: false
        options: '{"position":"topleft","title":"Reset View"}'
      map_scale:
        control: false
        options: '{"position":"bottomright","maxWidth":100,"metric":true,"imperial":false,"updateWhenIdle":false}'
      fullscreen:
        control: false
        options: '{"position":"topleft","pseudoFullscreen":false}'
      path: '{"color":"#3388ff","opacity":"1.0","stroke":true,"weight":3,"fill":"depends","fillColor":"*","fillOpacity":"0.2","radius":"6"}'
      locate:
        control: false
        options: '{"position":"topright","setView":"untilPanOrZoom","returnToPrevBounds":true,"keepCurrentZoomLevel":true,"strings":{"title":"Locate my position"}}'
        automatic: false
      geocoder:
        control: false
        settings:
          position: topright
          input_size: 20
          providers: {  }
          min_terms: 4
          delay: 800
          zoom: 16
          popup: false
          options: ''
      geometry_validation: 0
      feature_properties:
        values: ''
    third_party_settings: {  }
  field_search_inflection:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  description: true
  path: true
  simple_sitemap: true
