uuid: 4ccc0064-609e-4fa7-8cc2-fbb33897e8d5
langcode: en
status: true
dependencies:
  config:
    - field.field.node.simplenews_issue.body
    - field.field.node.simplenews_issue.field_regions_of_interest
    - field.field.node.simplenews_issue.field_scheduled
    - field.field.node.simplenews_issue.simplenews_issue
    - node.type.simplenews_issue
  module:
    - datetime
    - datetimehideseconds
    - simplenews
    - text
_core:
  default_config_hash: 95gz1Cn7UjP1ED-lgXnR9ucmWPz9TOn1YEuKUllmELo
id: node.simplenews_issue.default
targetEntityType: node
bundle: simplenews_issue
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 4
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  field_regions_of_interest:
    type: entity_reference_autocomplete
    weight: 6
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_scheduled:
    type: datetime_default
    weight: 3
    region: content
    settings: {  }
    third_party_settings:
      datetimehideseconds:
        hide: true
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  simplenews_issue:
    type: simplenews_issue
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  path: true
  promote: true
  simple_sitemap: true
  sticky: true
  uid: true
  url_redirects: true
