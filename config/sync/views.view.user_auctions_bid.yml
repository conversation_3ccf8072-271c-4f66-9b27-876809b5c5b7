uuid: b461a3ce-3d4b-41c0-bf3b-fddd9c1f9cfe
langcode: en
status: true
dependencies:
  module:
    - auctions
    - user
    - views_aggregator
id: user_auctions_bid
label: 'User auctions bid'
module: views
description: ''
tag: ''
base_table: users_field_data
base_field: uid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Participated auctions'
      fields:
        name:
          id: name
          table: users_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: name
          plugin_id: field
          label: User
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: user_name
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        uid:
          id: uid
          table: users_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: uid
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        id:
          id: id
          table: auction_field_data
          field: id
          relationship: auction
          group_type: count_distinct
          admin_label: ''
          entity_type: auction
          entity_field: id
          plugin_id: field
          label: 'Participated auctions'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: true
            path: '/user/{{ uid }}/participated'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: null
          group_columns: null
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ''
          field_api_classes: false
          set_precision: false
          precision: 0
          decimal: .
          format_plural: 0
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 100
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access user profiles'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments: {  }
      filters:
        status:
          id: status
          table: users_field_data
          field: status
          entity_type: user
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        uid:
          id: uid
          table: users_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: uid
          plugin_id: user_name
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: uid_op
            label: Bidder
            description: ''
            use_operator: false
            operator: uid_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: uid
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        uid_1:
          id: uid_1
          table: users_field_data
          field: uid
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: uid
          plugin_id: user_name
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: uid_1_op
            label: 'Auction owner'
            description: ''
            use_operator: false
            operator: uid_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: uid_1
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        created:
          id: created
          table: bid
          field: created
          relationship: reverse__bid__uid
          group_type: group
          admin_label: ''
          entity_type: bid
          entity_field: created
          plugin_id: date
          operator: '>='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: created_op
            label: From
            description: ''
            use_operator: false
            operator: created_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: from
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        created_1:
          id: created_1
          table: bid
          field: created
          relationship: reverse__bid__uid
          group_type: group
          admin_label: ''
          entity_type: bid
          entity_field: created
          plugin_id: date
          operator: '<='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: created_1_op
            label: To
            description: ''
            use_operator: false
            operator: created_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: to
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: views_aggregator_plugin_style_table
        options:
          row_class: ''
          default_row_class: true
          columns:
            name: name
            uid: uid
            id: id
          default: id
          info:
            name:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
              has_aggr: false
              aggr:
                views_aggregator_first: views_aggregator_first
              aggr_par: ''
              has_aggr_column: false
              aggr_column: views_aggregator_sum
              aggr_par_column: ''
            uid:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
              has_aggr: false
              aggr:
                views_aggregator_first: views_aggregator_first
              aggr_par: ''
              has_aggr_column: false
              aggr_column: views_aggregator_sum
              aggr_par_column: ''
            id:
              sortable: false
              default_sort_order: desc
              align: views-align-left
              separator: ''
              empty_column: false
              responsive: ''
              has_aggr: false
              aggr:
                views_aggregator_count: views_aggregator_count
                views_aggregator_group_and_compress: views_aggregator_group_and_compress
              aggr_par: ''
              has_aggr_column: true
              aggr_column: views_aggregator_sum
              aggr_par_column: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
          group_aggregation:
            group_aggregation_results: 0
            grouping_field_class: ''
            result_label_prefix: ''
            result_label_suffix: ''
            grouping_row_class: ''
          column_aggregation:
            totals_row_position:
              1: 1
              2: 0
              3: 0
            totals_per_page: 0
            precision: 0
            totals_row_class: ''
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__bid__uid:
          id: reverse__bid__uid
          table: users_field_data
          field: reverse__bid__uid
          relationship: none
          group_type: group
          admin_label: Bid
          entity_type: user
          plugin_id: standard
          required: false
        auction:
          id: auction
          table: bid
          field: auction
          relationship: reverse__bid__uid
          group_type: group
          admin_label: Auction
          entity_type: bid
          entity_field: auction
          plugin_id: standard
          required: false
        uid:
          id: uid
          table: auction_field_data
          field: uid
          relationship: auction
          group_type: group
          admin_label: User
          entity_type: auction
          entity_field: uid
          plugin_id: standard
          required: true
      group_by: true
      header:
        result:
          id: result
          table: views
          field: result
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: result
          empty: true
          content: 'Displaying @start - @end of @total'
      footer: {  }
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  overview:
    id: overview
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: admin/people/bids
      menu:
        type: tab
        title: 'User participated auctions'
        description: ''
        weight: -10
        expanded: false
        menu_name: admin
        parent: entity.user.collection
        context: '1'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
