uuid: 6449b265-fc8b-4ea9-811e-344a40b67d2b
langcode: en
status: true
dependencies:
  config:
    - field.field.node.auction_offers.field_auction_offer
    - field.field.node.auction_offers.field_auction_reference
    - field.field.node.auction_offers.field_user_reference
    - node.type.auction_offers
  module:
    - path
id: node.auction_offers.default
targetEntityType: node
bundle: auction_offers
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_auction_offer:
    type: number
    weight: 123
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_auction_reference:
    type: entity_reference_autocomplete
    weight: 121
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_user_reference:
    type: entity_reference_autocomplete
    weight: 122
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  simple_sitemap:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
