uuid: c77f4a12-9390-4c6f-8e3a-fb6b07ecb099
langcode: en
status: true
dependencies:
  config:
    - commerce_invoice.commerce_invoice_type.default
    - field.field.commerce_invoice.default.field_warnings
  module:
    - commerce_invoice
    - mezabirza
    - state_machine
    - user
id: commerce_invoice.default.default
targetEntityType: commerce_invoice
bundle: default
mode: default
content:
  due_date:
    type: timestamp
    label: above
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: long
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 5
    region: content
  invoice_date:
    type: timestamp
    label: above
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: long
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 4
    region: content
  invoice_items:
    type: commerce_invoice_item_table
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  mail:
    type: max_width_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  state:
    type: state_transition_form
    label: hidden
    settings:
      require_confirmation: false
      use_modal: false
    third_party_settings: {  }
    weight: 6
    region: content
  total_price:
    type: commerce_invoice_total_summary
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  uid:
    type: author
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  billing_profile: true
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_warnings: true
  invoice_file: true
  invoice_number: true
  langcode: true
  orders: true
  store_id: true
  total_paid: true
