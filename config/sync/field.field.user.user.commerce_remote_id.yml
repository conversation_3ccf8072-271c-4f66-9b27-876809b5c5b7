uuid: 1ddce326-0498-4d49-bda7-f83abfdf9b07
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.commerce_remote_id
  module:
    - commerce
    - user
  enforced:
    module:
      - commerce_payment
_core:
  default_config_hash: Zu4REVGzMPlwpQVE7u1sM_74HqLCp0u42VNWlAwZcIw
id: user.user.commerce_remote_id
field_name: commerce_remote_id
entity_type: user
bundle: user
label: 'Remote ID'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: commerce_remote_id
