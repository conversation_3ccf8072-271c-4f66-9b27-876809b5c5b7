uuid: f795921d-82a1-4328-9d67-5be1f1979966
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_purchase_agreement
  module:
    - file
    - user
id: user.user.field_purchase_agreement
field_name: field_purchase_agreement
entity_type: user
bundle: user
label: 'Purchase agreement'
description: 'Here you can add your default purchase agreement which will be added to your auctions by default.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: 'user/auction_agreements/[date:custom:Y]-[date:custom:m]'
  file_extensions: 'pdf doc docx'
  max_filesize: ''
  description_field: false
field_type: file
