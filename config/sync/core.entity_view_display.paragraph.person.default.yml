uuid: c2048799-5bee-4712-9c9c-39c9b45093f1
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.person.field_content
    - field.field.paragraph.person.field_image
    - image.style.person
    - paragraphs.paragraphs_type.person
  module:
    - image
    - text
id: paragraph.person.default
targetEntityType: paragraph
bundle: person
mode: default
content:
  field_content:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: person
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
