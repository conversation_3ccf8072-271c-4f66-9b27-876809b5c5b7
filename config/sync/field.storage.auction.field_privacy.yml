uuid: e47b3103-c99c-4e0a-bb61-089eb56ad249
langcode: en
status: true
dependencies:
  module:
    - auctions
    - options
id: auction.field_privacy
field_name: field_privacy
entity_type: auction
type: list_string
settings:
  allowed_values:
    -
      value: show_attachments_unauthorised
      label: 'Show attachments to unauthorised users'
  allowed_values_function: ''
module: options
locked: false
cardinality: -1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
