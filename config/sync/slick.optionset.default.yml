uuid: 1a23d72c-c994-41f3-b722-964f8b858523
langcode: en
status: false
dependencies:
  enforced:
    module:
      - slick
_core:
  default_config_hash: jHmfPBU2oNraKWjKwqYODaPOKQQM2sczj5TlDlN8Om8
id: default
name: default
weight: -10
label: Default
group: ''
skin: default
breakpoints: 0
optimized: false
options:
  options__active_tab: ''
  settings:
    mobileFirst: false
    asNavFor: ''
    accessibility: true
    regionLabel: carousel
    useGroupRole: true
    instructionsText: ''
    adaptiveHeight: false
    autoplay: false
    useAutoplayToggleButton: true
    pauseIcon: slick-pause-icon
    playIcon: slick-play-icon
    pauseOnHover: true
    pauseOnDotsHover: false
    pauseOnFocus: true
    autoplaySpeed: 8000
    arrows: true
    prevArrow: Previous
    nextArrow: Next
    arrowsPlacement: ''
    downArrow: false
    downArrowTarget: ''
    downArrowOffset: 0
    centerMode: false
    centerPadding: 50px
    dots: false
    dotsClass: slick-dots
    appendDots: $(element)
    draggable: true
    fade: false
    focusOnSelect: false
    infinite: true
    initialSlide: 0
    lazyLoad: ondemand
    mouseWheel: false
    randomize: false
    respondTo: window
    rtl: false
    rows: 1
    slidesPerRow: 1
    slide: ''
    slidesToShow: 1
    slidesToScroll: 1
    speed: 500
    swipe: true
    swipeToSlide: false
    edgeFriction: 0.35
    touchMove: true
    touchThreshold: 5
    useCSS: true
    cssEase: ease
    cssEaseBezier: ''
    cssEaseOverride: ''
    useTransform: true
    easing: linear
    variableWidth: false
    vertical: false
    verticalSwiping: false
    waitForAnimate: true
