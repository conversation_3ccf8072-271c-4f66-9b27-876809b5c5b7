uuid: da3dad90-8cfe-4b74-8374-67b50702a79c
langcode: en
status: true
dependencies:
  config:
    - commerce_product.commerce_product_type.default
    - field.field.commerce_product.default.body
  module:
    - commerce
    - commerce_product
    - path
    - text
_core:
  default_config_hash: bVqhDE0CBvUi4RPHqKu4sBhUOUwOTgcHVhn5PkfngLU
id: commerce_product.default.default
targetEntityType: commerce_product
bundle: default
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 2
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  stores:
    type: commerce_entity_select
    weight: 0
    region: content
    settings:
      hide_single_entity: true
      autocomplete_threshold: 7
      autocomplete_size: 60
      autocomplete_placeholder: ''
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  variations:
    type: commerce_product_single_variation
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
