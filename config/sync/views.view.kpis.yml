uuid: 7a083d9f-1ef7-4458-b759-e68e4ee9555b
langcode: en
status: true
dependencies:
  module:
    - better_exposed_filters
    - elasticsearch_handler
    - inqube
    - user
id: kpis
label: KPIs
module: views
description: ''
tag: ''
base_table: elasticsearch_result
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: KPIs
      fields:
        inqueb_source:
          id: inqueb_source
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: Auction
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: id
          sort_field: label
          load_as_entity: auction
          link_to_entity: 1
          convert_to_link: 0
        inqueb_source_1:
          id: inqueb_source_1
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: 'End time'
          exclude: false
          alter:
            alter_text: true
            text: "{{ inqueb_source_1 |date('d.m.Y H:i:s') }}"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: end_time
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
        inqueb_source_2:
          id: inqueb_source_2
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: 'Cutting area (ha)'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: cutting_area
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
        inqueb_source_3:
          id: inqueb_source_3
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: 'Forest volume (m3)'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: forest_volume
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
        inqueb_source_4:
          id: inqueb_source_4
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: 'Start price (EUR)'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: start_price
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
        inqueb_source_5:
          id: inqueb_source_5
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: 'End price (EUR)'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: current_price
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
        inqueb_source_6:
          id: inqueb_source_6
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: Bids
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: bids
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
        inqueb_source_7:
          id: inqueb_source_7
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: 'Price increase (%)'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: price_increase
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
        inqueb_source_8:
          id: inqueb_source_8
          table: elasticsearch_result
          field: inqueb_source
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: inqube_elasticsearch_source
          label: 'Price per m3 (EUR)'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          source_field: price_m3
          sort_field: ''
          load_as_entity: ''
          link_to_entity: 0
          convert_to_link: 0
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 200
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: full
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_hide: false
              input_required: true
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
              autosubmit_textfield_minimum_length: 3
            filter:
              filter_auction_species:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: true
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              filter_price_group:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: true
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              filter_from:
                plugin_id: default
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              filter_to:
                plugin_id: default
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              filter_has_bids:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: true
                select_all_none_nested: false
                display_inline: false
      access:
        type: perm
        options:
          perm: 'access content overview'
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No auctions found'
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        filter_auction_species:
          id: filter_auction_species
          table: elasticsearch_result
          field: filter_auction_species
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_species
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: filter_auction_species_op
            label: Species
            description: ''
            use_operator: false
            operator: filter_auction_species_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_auction_species
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_price_group:
          id: filter_price_group
          table: elasticsearch_result
          field: filter_price_group
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_price_group
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: filter_price_group_op
            label: 'Price group'
            description: ''
            use_operator: false
            operator: filter_price_group_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_price_group
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_from:
          id: filter_from
          table: elasticsearch_result
          field: filter_from
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: elastic_date
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: filter_from_op
            label: From
            description: ''
            use_operator: false
            operator: filter_from_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: from
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_to:
          id: filter_to
          table: elasticsearch_result
          field: filter_to
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: elastic_date
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: filter_to_op
            label: To
            description: ''
            use_operator: false
            operator: filter_to_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: to
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_has_bids:
          id: filter_has_bids
          table: elasticsearch_result
          field: filter_has_bids
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '0'
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Include auctions without bids'
            description: ''
            use_operator: false
            operator: filter_has_bids_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_has_bids
            required: true
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
          is_grouped: false
          group_info:
            label: 'Has bids'
            description: ''
            identifier: filter_has_bids
            optional: true
            widget: select
            multiple: true
            remember: false
            default_group: '1'
            default_group_multiple:
              1: 1
            group_items:
              1:
                title: 'With bids'
                operator: '='
                value: '1'
              2:
                title: 'Without bids'
                operator: '='
                value: '0'
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            inqueb_source: inqueb_source
            inqueb_source_1: inqueb_source_1
            inqueb_source_2: inqueb_source_2
            inqueb_source_3: inqueb_source_3
            inqueb_source_4: inqueb_source_4
            inqueb_source_5: inqueb_source_5
            inqueb_source_6: inqueb_source_6
            inqueb_source_7: inqueb_source_7
            inqueb_source_8: inqueb_source_8
          default: inqueb_source_1
          info:
            inqueb_source:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_1:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_2:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_3:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_4:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_5:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_6:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_7:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            inqueb_source_8:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_builder: auctions
          entity_relationship:
            entity_type_key: _source.entity
            entity_id_key: _source.id
      relationships: {  }
      header:
        kpi_area:
          id: kpi_area
          table: elasticsearch_result
          field: kpi_area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: kpi_area
          empty: false
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  page:
    id: page
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history: {  }
      path: admin/content/kpi
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
