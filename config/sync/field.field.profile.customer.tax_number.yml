uuid: 526f94a5-78ee-41a0-aec4-535c3e84b936
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.tax_number
    - profile.type.customer
  module:
    - commerce_tax
  enforced:
    module:
      - commerce_tax
_core:
  default_config_hash: CGcN7vNeKKjcD0PjtvSXOchhJll9sHwastAFsjBsfqI
id: profile.customer.tax_number
field_name: tax_number
entity_type: profile
bundle: customer
label: 'Tax number'
description: ''
required: false
translatable: false
default_value:
  - {  }
default_value_callback: ''
settings:
  countries:
    EU: EU
    LV: LV
  verify: false
  allow_unverified: true
field_type: commerce_tax_number
