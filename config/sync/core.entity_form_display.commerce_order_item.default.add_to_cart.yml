uuid: 17c583c0-a0fe-43e5-ae0d-981e97b97671
langcode: en
status: true
dependencies:
  config:
    - commerce_order.commerce_order_item_type.default
    - core.entity_form_mode.commerce_order_item.add_to_cart
  module:
    - commerce_product
  enforced:
    module:
      - commerce_cart
      - commerce_product
_core:
  default_config_hash: 8dIszGyXfy-kBaUEuUEQjWjRVtfq6f8cCI0QUHIlJdc
id: commerce_order_item.default.add_to_cart
targetEntityType: commerce_order_item
bundle: default
mode: add_to_cart
content:
  purchased_entity:
    type: commerce_product_variation_attributes
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  quantity: true
  status: true
  uid: true
  unit_price: true
