uuid: 23d300d8-739f-4129-ad78-20b2a75a5e1c
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.storage.auction.field_property_forest_size
  module:
    - require_on_publish
third_party_settings:
  require_on_publish:
    require_on_publish: true
id: auction.stumpage.field_property_forest_size
field_name: field_property_forest_size
entity_type: auction
bundle: stumpage
label: 'Forest area'
description: 'Total forest area in the proposed property or properties. It can be found in the State Land Cadastre in the section "Distribution of land area by types of use", expressed in ha.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ' ha'
field_type: float
