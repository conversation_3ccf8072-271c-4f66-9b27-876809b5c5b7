uuid: bf8618c9-8ee5-4f00-810c-378f7fbb33b3
langcode: en
status: true
dependencies:
  module:
    - options
    - paragraphs
id: paragraph.field_specie
field_name: field_specie
entity_type: paragraph
type: list_string
settings:
  allowed_values:
    -
      value: 'NV dastots'
      label: 'Nav dastots'
    -
      value: '<PERSON> <PERSON>riede'
      label: Priede
    -
      value: 'E Egle'
      label: Egle
    -
      value: 'B Bērzs'
      label: Bērzs
    -
      value: 'A Apse'
      label: Apse
    -
      value: 'M Melnalksnis'
      label: Melnalksnis
    -
      value: 'Ba Baltalksnis'
      label: Baltalksnis
    -
      value: 'Oz Ozols'
      label: Ozols
    -
      value: 'Os Osis'
      label: Osis
    -
      value: 'L Liepa'
      label: Liepa
    -
      value: 'Bl Blīgzna'
      label: Blīgzna
    -
      value: 'Kl Kļava'
      label: Kļava
    -
      value: 'SE Sausk. Egle'
      label: 'Sausk. Egle'
    -
      value: 'SP Sausk. Priede'
      label: 'Sausk. Priede'
    -
      value: Cits
      label: Cits
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
