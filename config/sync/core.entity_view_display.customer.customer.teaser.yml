uuid: 4c7c1023-7845-4f09-b6c8-01c70d4ef480
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.customer.teaser
    - field.field.customer.customer.field_tags
    - field.field.customer.customer.field_tasks
  module:
    - customer_relationship
    - text
id: customer.customer.teaser
targetEntityType: customer
bundle: customer
mode: teaser
content:
  description:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_tags: true
  field_tasks: true
  uid: true
