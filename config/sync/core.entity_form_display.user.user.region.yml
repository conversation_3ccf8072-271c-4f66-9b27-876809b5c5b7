uuid: f5fe64cd-b79d-4226-a943-7dab28b35a0b
langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.user.region
    - field.field.user.user.commerce_remote_id
    - field.field.user.user.field_auction_notifications
    - field.field.user.user.field_bid_notifications
    - field.field.user.user.field_block_users
    - field.field.user.user.field_display_name
    - field.field.user.user.field_domain
    - field.field.user.user.field_domain_admin
    - field.field.user.user.field_extra_maps
    - field.field.user.user.field_from
    - field.field.user.user.field_full_name
    - field.field.user.user.field_interests
    - field.field.user.user.field_legal_entity
    - field.field.user.user.field_locations
    - field.field.user.user.field_phone
    - field.field.user.user.field_purchase_agreement
    - field.field.user.user.field_rating
    - field.field.user.user.field_regions_of_interest
    - field.field.user.user.field_registration_number
    - field.field.user.user.field_status
  module:
    - user
id: user.user.region
targetEntityType: user
bundle: user
mode: region
content:
  google_analytics:
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  language:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  timezone:
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  account: true
  commerce_remote_id: true
  customer_profiles: true
  field_auction_notifications: true
  field_bid_notifications: true
  field_block_users: true
  field_display_name: true
  field_domain: true
  field_domain_admin: true
  field_extra_maps: true
  field_from: true
  field_full_name: true
  field_interests: true
  field_legal_entity: true
  field_locations: true
  field_phone: true
  field_purchase_agreement: true
  field_rating: true
  field_regions_of_interest: true
  field_registration_number: true
  field_status: true
  langcode: true
  path: true
  simplenews: true
