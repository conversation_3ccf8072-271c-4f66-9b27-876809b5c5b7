uuid: 02337000-716e-4bf8-abef-e992708bd6a7
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.field.auction.stumpage.field_360_images
    - field.field.auction.stumpage.field_administrative_area
    - field.field.auction.stumpage.field_agreement
    - field.field.auction.stumpage.field_agreement_signing
    - field.field.auction.stumpage.field_agreement_type
    - field.field.auction.stumpage.field_attachments
    - field.field.auction.stumpage.field_auction_type
    - field.field.auction.stumpage.field_bidding_method
    - field.field.auction.stumpage.field_cadastre_number
    - field.field.auction.stumpage.field_comments
    - field.field.auction.stumpage.field_custom_auction
    - field.field.auction.stumpage.field_cutting_area
    - field.field.auction.stumpage.field_cutting_type
    - field.field.auction.stumpage.field_delivery_conditions
    - field.field.auction.stumpage.field_extra_emails
    - field.field.auction.stumpage.field_fees
    - field.field.auction.stumpage.field_felling_certificate_files
    - field.field.auction.stumpage.field_felling_certificate_num
    - field.field.auction.stumpage.field_forest_volume
    - field.field.auction.stumpage.field_forwarding_distance
    - field.field.auction.stumpage.field_images
    - field.field.auction.stumpage.field_liz_score
    - field.field.auction.stumpage.field_map
    - field.field.auction.stumpage.field_map_image
    - field.field.auction.stumpage.field_measurements_not_made
    - field.field.auction.stumpage.field_notifications_sent
    - field.field.auction.stumpage.field_object
    - field.field.auction.stumpage.field_other_participants
    - field.field.auction.stumpage.field_owner_confirmation_file
    - field.field.auction.stumpage.field_ownership
    - field.field.auction.stumpage.field_participants
    - field.field.auction.stumpage.field_previous_price
    - field.field.auction.stumpage.field_price_group
    - field.field.auction.stumpage.field_privacy
    - field.field.auction.stumpage.field_private
    - field.field.auction.stumpage.field_property_area_size
    - field.field.auction.stumpage.field_property_auction
    - field.field.auction.stumpage.field_property_forest_condition
    - field.field.auction.stumpage.field_property_forest_size
    - field.field.auction.stumpage.field_property_name
    - field.field.auction.stumpage.field_purchase_agreement
    - field.field.auction.stumpage.field_quantity
    - field.field.auction.stumpage.field_route_and_strorage
    - field.field.auction.stumpage.field_species_composition
    - field.field.auction.stumpage.field_species_list
    - field.field.auction.stumpage.field_step
    - field.field.auction.stumpage.field_stumpage_auction
    - field.field.auction.stumpage.field_total_forest_stock
    - field.field.auction.stumpage.field_video
    - field.field.auction.stumpage.field_view_count
    - image.style.related
  module:
    - auctions
    - baguettebox
    - datetime
    - entity_reference_revisions
    - field_group
    - file
    - image_field_360
    - leaflet
    - mezabirza
    - options
    - text
third_party_settings:
  field_group:
    group_species:
      children:
        - field_species_list
        - field_species_composition
        - field_price_group
      label: 'Species list'
      parent_name: ''
      region: content
      weight: 34
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
    group_distances:
      children:
        - distance_to_object
      label: Distances
      parent_name: ''
      region: content
      weight: 35
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        open: false
        description: ''
    group_transportation:
      children:
        - field_route_and_strorage
        - field_delivery_conditions
        - field_forwarding_distance
      label: Transportation
      parent_name: ''
      region: content
      weight: 36
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: false
id: auction.stumpage.default
targetEntityType: auction
bundle: stumpage
mode: default
content:
  add_to_calendar:
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
  administrative_area:
    type: entity_reference_label
    label: hidden
    settings:
      link: false
    third_party_settings: {  }
    weight: 3
    region: content
  auction_type:
    settings: {  }
    third_party_settings: {  }
    weight: 37
    region: content
  auction_winner:
    settings: {  }
    third_party_settings: {  }
    weight: 24
    region: content
  bid_form:
    settings: {  }
    third_party_settings: {  }
    weight: 8
    region: content
  bid_offer_form:
    settings: {  }
    third_party_settings: {  }
    weight: 31
    region: content
  commission:
    settings: {  }
    third_party_settings: {  }
    weight: 26
    region: content
  cubic_price:
    settings: {  }
    third_party_settings: {  }
    weight: 21
    region: content
  currency:
    type: list_key
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 19
    region: content
  current_price:
    type: auction_amount
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 7
    region: content
  description:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 11
    region: content
  distance_to_object:
    settings: {  }
    third_party_settings: {  }
    weight: 41
    region: content
  end_summary:
    settings: {  }
    third_party_settings: {  }
    weight: 20
    region: content
  end_summary_offer:
    settings: {  }
    third_party_settings: {  }
    weight: 33
    region: content
  end_time:
    type: datetime_default
    label: inline
    settings:
      timezone_override: ''
      format_type: short
    third_party_settings: {  }
    weight: 5
    region: content
  field_360_images:
    type: Image_field_360
    label: hidden
    settings:
      loading_msg: Loading...
      width: auto
      height: 160px
      navbar_enable: '1'
      navbar_backgroundColor: 'rgba(61, 61, 61, 0.5)'
      navbar_buttonsColor: 'rgba(255, 255, 255, 0.7)'
      navbar_buttonsBackgroundColor: transparent
      navbar_activeButtonsBackgroundColor: 'rgba(255, 255, 255, 0.1)'
      navbar_buttonsHeight: '20'
      navbar_autorotateThickness: '1'
      navbar_zoomRangeWidth: '50'
      navbar_zoomRangeThickness: '1'
      navbar_zoomRangeDisk: '7'
      navbar_fullscreenRatio: 4/3
      navbar_fullscreenThickness: '2'
    third_party_settings: {  }
    weight: 54
    region: content
  field_administrative_area:
    type: mb_entity_reference_shs
    label: inline
    settings:
      link: false
    third_party_settings: {  }
    weight: 9
    region: content
  field_agreement_signing:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 47
    region: content
  field_agreement_type:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 45
    region: content
  field_attachments:
    type: file_default
    label: hidden
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 17
    region: content
  field_auction_type:
    type: list_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 50
    region: content
  field_bidding_method:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 55
    region: content
  field_cadastre_number:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 12
    region: content
  field_cutting_area:
    type: number_decimal
    label: inline
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 14
    region: content
  field_cutting_type:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 13
    region: content
  field_delivery_conditions:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 35
    region: content
  field_felling_certificate_files:
    type: file_default
    label: inline
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 30
    region: content
  field_felling_certificate_num:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 29
    region: content
  field_forest_volume:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 15
    region: content
  field_forwarding_distance:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 36
    region: content
  field_images:
    type: baguettebox
    label: hidden
    settings:
      image_style: related
      baguette_image_style: ''
      baguette_image_style_responsive:
        -
          width: null
          image_style: ''
        -
          width: null
          image_style: ''
        -
          width: null
          image_style: ''
        -
          width: null
          image_style: ''
        -
          width: null
          image_style: ''
      animation: slideIn
      captions_source: image_alt
      buttons: true
      fullscreen: false
      hide_scrollbars: true
      inline: true
    third_party_settings: {  }
    weight: 16
    region: content
  field_liz_score:
    type: number_integer
    label: inline
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 58
    region: content
  field_map:
    type: leaflet_formatter_default
    label: hidden
    settings:
      multiple_map: false
      leaflet_map: 'Auction widget'
      height: 400
      height_unit: px
      hide_empty_map: true
      disable_wheel: true
      gesture_handling: false
      fitbounds_options: '{"padding":[0,0]}'
      reset_map:
        control: false
        position: topright
      map_scale:
        control: false
        options: '{"position":"bottomright","maxWidth":100,"metric":true,"imperial":false,"updateWhenIdle":false}'
      locate:
        control: false
        options: '{"position": "topright", "setView": "untilPanOrZoom", "returnToPrevBounds":true, "keepCurrentZoomLevel": true, "strings": {"title": "Locate my position"}}'
        automatic: false
      leaflet_tooltip:
        value: ''
        options: '{"permanent":false,"direction":"center"}'
      popup: false
      popup_content: ''
      leaflet_popup:
        control: ''
        content: ''
        options: '{"maxWidth":"300","minWidth":"50", "autoPan": true}'
        value: ''
        view_mode: full
      map_position:
        force: false
        center:
          lat: 0.0
          lon: 0.0
        zoom: 11
        minZoom: 1
        maxZoom: 18
        zoomFiner: 0
      weight: '0'
      icon:
        iconType: marker
        iconUrl: ''
        shadowUrl: ''
        className: ''
        iconSize:
          x: ''
          'y': ''
        iconAnchor:
          x: ''
          'y': ''
        shadowSize:
          x: ''
          'y': ''
        shadowAnchor:
          x: ''
          'y': ''
        popupAnchor:
          x: ''
          'y': ''
        html: '<div></div>'
        html_class: leaflet-map-divicon
        circle_marker_options: '{"radius":100,"color":"red","fillColor":"#f03","fillOpacity":0.5}'
      leaflet_markercluster:
        control: false
        options: '{"spiderfyOnMaxZoom":true,"showCoverageOnHover":true,"removeOutsideVisibleBounds":false}'
        include_path: false
      fullscreen:
        control: true
        options: '{"position":"topleft","pseudoFullscreen":false}'
      path: '{"color":"#3388ff","opacity":"1.0","stroke":true,"weight":3,"fill":"depends","fillColor":"*","fillOpacity":"0.0"}'
      feature_properties:
        values: ''
      geocoder:
        control: false
        settings:
          popup: false
          position: topright
          input_size: 25
          providers: {  }
          min_terms: 4
          delay: 800
          zoom: 16
          options: ''
      map_lazy_load:
        lazy_load: false
    third_party_settings: {  }
    weight: 18
    region: content
  field_object:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 52
    region: content
  field_owner_confirmation_file:
    type: file_default
    label: inline
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 49
    region: content
  field_ownership:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 48
    region: content
  field_participants:
    type: entity_reference_label
    label: inline
    settings:
      link: false
    third_party_settings: {  }
    weight: 27
    region: content
  field_price_group:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 36
    region: content
  field_private:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 25
    region: content
  field_property_area_size:
    type: number_decimal
    label: inline
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 39
    region: content
  field_property_auction:
    type: boolean
    label: inline
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 41
    region: content
  field_property_forest_condition:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 38
    region: content
  field_property_forest_size:
    type: number_decimal
    label: inline
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 40
    region: content
  field_property_name:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 10
    region: content
  field_purchase_agreement:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 46
    region: content
  field_quantity:
    type: number_decimal
    label: inline
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 51
    region: content
  field_route_and_strorage:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 34
    region: content
  field_species_composition:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 35
    region: content
  field_species_list:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 34
    region: content
  field_stumpage_auction:
    type: boolean
    label: inline
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 42
    region: content
  field_total_forest_stock:
    type: number_decimal
    label: inline
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 43
    region: content
  field_video:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 44
    region: content
  field_view_count:
    type: number_integer
    label: hidden
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 23
    region: content
  member_for:
    settings: {  }
    third_party_settings: {  }
    weight: 22
    region: content
  more_info_on_empty_auction:
    settings: {  }
    third_party_settings: {  }
    weight: 57
    region: content
  operations:
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  price:
    type: number_decimal
    label: inline
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 1
    region: content
  price_per_ha:
    settings: {  }
    third_party_settings: {  }
    weight: 32
    region: content
  price_per_quantity:
    settings: {  }
    third_party_settings: {  }
    weight: 53
    region: content
  rate:
    settings: {  }
    third_party_settings: {  }
    weight: 56
    region: content
  remain_time:
    type: datetime_end_time
    label: inline
    settings:
      timezone_override: ''
      format_type: short
    third_party_settings: {  }
    weight: 6
    region: content
  start_price:
    type: auction_amount
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: content
  subscribe:
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  title:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  user_auction_status:
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  waze:
    settings: {  }
    third_party_settings: {  }
    weight: 28
    region: content
hidden:
  bids: true
  created: true
  cta_bid: true
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  fee: true
  field_agreement: true
  field_comments: true
  field_custom_auction: true
  field_extra_emails: true
  field_fees: true
  field_map_image: true
  field_measurements_not_made: true
  field_notifications_sent: true
  field_other_participants: true
  field_previous_price: true
  field_privacy: true
  field_step: true
  langcode: true
  start_time: true
  status: true
  uid: true
