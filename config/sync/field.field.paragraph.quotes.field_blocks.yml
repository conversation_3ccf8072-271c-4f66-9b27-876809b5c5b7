uuid: 8bd57d01-6327-47de-abc7-7d2b60d226de
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_blocks
    - paragraphs.paragraphs_type.quote
    - paragraphs.paragraphs_type.quotes
  module:
    - entity_reference_revisions
id: paragraph.quotes.field_blocks
field_name: field_blocks
entity_type: paragraph
bundle: quotes
label: Quote
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      quote: quote
    negate: 0
    target_bundles_drag_drop:
      accordion_item:
        weight: 10
        enabled: false
      banner:
        weight: 11
        enabled: false
      calculator:
        weight: 12
        enabled: false
      quote:
        weight: 14
        enabled: true
      quotes:
        weight: 13
        enabled: false
      slide:
        weight: 14
        enabled: false
      slideshow:
        weight: 15
        enabled: false
      text:
        weight: 16
        enabled: false
      title:
        weight: 17
        enabled: false
      view:
        weight: 18
        enabled: false
field_type: entity_reference_revisions
