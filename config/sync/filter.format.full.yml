uuid: 321edce4-8f4f-4023-9008-d5710ffdb4da
langcode: en
status: true
dependencies:
  module:
    - blazy
    - ckeditor5_embedded_content
    - editor
    - inline_responsive_images
    - media
    - mezabirza
    - slick
    - url_embed
name: Full
format: full
weight: -10
filters:
  blazy_filter:
    id: blazy_filter
    provider: blazy
    status: false
    weight: -35
    settings:
      media_switch: ''
      hybrid_style: ''
      box_style: ''
      box_media_style: ''
      box_caption: ''
      filter_tags:
        img: img
        iframe: iframe
      use_data_uri: '0'
  ckeditor5_embedded_content:
    id: ckeditor5_embedded_content
    provider: ckeditor5_embedded_content
    status: true
    weight: -40
    settings: {  }
  editor_file_reference:
    id: editor_file_reference
    provider: editor
    status: true
    weight: -49
    settings: {  }
  filter_align:
    id: filter_align
    provider: filter
    status: true
    weight: -48
    settings: {  }
  filter_autop:
    id: filter_autop
    provider: filter
    status: true
    weight: -47
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: true
    weight: -46
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: false
    weight: -39
    settings:
      allowed_html: '<p class="highlight-info highlight-error font-small font-large font-extra-large"> <h1 class="font-green"> <h2 class="font-green green-title-block"> <h3 class="font-green"> <h4 class="font-green"> <a class="button button--primary button--secondary button--action button--cta button--outline button--danger button--primary--large button--secondary--large button--actio--large button--cta--large button--outline--large button--danger--large">'
      filter_html_help: true
      filter_html_nofollow: false
  filter_html_escape:
    id: filter_html_escape
    provider: filter
    status: false
    weight: -38
    settings: {  }
  filter_html_image_secure:
    id: filter_html_image_secure
    provider: filter
    status: false
    weight: -33
    settings: {  }
  filter_htmlcorrector:
    id: filter_htmlcorrector
    provider: filter
    status: true
    weight: -41
    settings: {  }
  filter_image_lazy_load:
    id: filter_image_lazy_load
    provider: filter
    status: true
    weight: -42
    settings: {  }
  filter_imagestyle:
    id: filter_imagestyle
    provider: inline_responsive_images
    status: true
    weight: -44
    settings:
      image_styles:
        article_image: article_image
        accordion_icon: '0'
        auction_full: '0'
        facebook: '0'
        geofield_map_default_icon_style: '0'
        hero: '0'
        icon: '0'
        large: '0'
        main_image_5: '0'
        map_image_desktop: '0'
        map_image_mobile: '0'
        map_image_mobile_small: '0'
        medium: '0'
        news_teaser: '0'
        news_teaser_medium: '0'
        news_teaser_small: '0'
        person: '0'
        related: '0'
        related_large: '0'
        slick_extra_small: '0'
        slick_media: '0'
        slick_mobile: '0'
        slick_tablet: '0'
        thumbnail: '0'
  filter_responsive_image_style:
    id: filter_responsive_image_style
    provider: inline_responsive_images
    status: false
    weight: -37
    settings:
      image_styles:
        map_teaser: '0'
        news_teaser: '0'
        related: '0'
        slick: '0'
  filter_style:
    id: filter_style
    provider: mezabirza
    status: true
    weight: -50
    settings: {  }
  filter_url:
    id: filter_url
    provider: filter
    status: true
    weight: -45
    settings:
      filter_url_length: 72
  media_embed:
    id: media_embed
    provider: media
    status: false
    weight: -32
    settings:
      default_view_mode: default
      allowed_view_modes: {  }
      allowed_media_types: {  }
  slick_filter:
    id: slick_filter
    provider: slick
    status: false
    weight: -34
    settings:
      caption:
        alt: '0'
        title: '0'
      optionset: default
      layout: ''
      admin_uri: '/en/admin/config/content/formats/manage/full?destination=/en/admin/config/content/formats&ajax_form=1&_wrapper_format=drupal_ajax'
      background: false
      box_caption: ''
      box_caption_custom: ''
      box_media_style: ''
      loading: ''
      responsive_image_style: ''
      box_style: ''
      image_style: ''
      media_switch: ''
      ratio: ''
      thumbnail_style: ''
      grid: ''
      grid_medium: ''
      grid_small: ''
      style: ''
      skin: ''
      overridables:
        arrows: '0'
        autoplay: '0'
        dots: '0'
        draggable: '0'
        infinite: '0'
        mouseWheel: '0'
        randomize: '0'
        variableWidth: '0'
      optionset_thumbnail: ''
      skin_thumbnail: ''
      thumbnail_caption: ''
      thumbnail_effect: ''
      thumbnail_position: ''
      override: false
      preserve_keys: false
      visible_items: null
  url_embed:
    id: url_embed
    provider: url_embed
    status: true
    weight: -43
    settings:
      enable_responsive: true
      default_ratio: '66.7'
  url_embed_convert_links:
    id: url_embed_convert_links
    provider: url_embed
    status: false
    weight: -36
    settings:
      url_prefix: ''
