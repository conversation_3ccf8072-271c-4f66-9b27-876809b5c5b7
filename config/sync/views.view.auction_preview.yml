uuid: 1958d6b1-066c-4e70-8ef9-417b297c1c96
langcode: en
status: true
dependencies:
  module:
    - better_exposed_filters
    - elasticsearch_handler
    - inqube
id: auction_preview
label: 'Auction preview'
module: views
description: ''
tag: ''
base_table: elasticsearch_result
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        rendered_entity:
          id: rendered_entity
          table: elasticsearch_result
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: elasticsearch_rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: '{{ rendered_entity }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode:
            -
              'auction:stumpage': teaser
          set_result_on_entity: 0
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 27
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: full
          bef:
            general:
              autosubmit: true
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_textfield_minimum_length: 3
              autosubmit_hide: true
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
            filter:
              filter_auction_type:
                plugin_id: bef
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: true
      access:
        type: none
        options: {  }
      cache:
        type: none
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: '<p data-align="center">There are no active auctions.</p>'
            format: full
          tokenize: false
      sorts:
        end_time:
          id: end_time
          table: elasticsearch_result
          field: end_time
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments: {  }
      filters:
        filter_auction_type:
          id: filter_auction_type
          table: elasticsearch_result
          field: filter_auction_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_type
          operator: in
          value:
            any: any
          group: 1
          exposed: true
          expose:
            operator_id: filter_auction_type_op
            label: ''
            description: ''
            use_operator: false
            operator: filter_auction_type_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_auction_type
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_auction_status:
          id: filter_auction_status
          table: elasticsearch_result
          field: filter_auction_status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_status
          operator: in
          value:
            active: active
            canceled: canceled
            finished: finished
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping:
            -
              field: rendered_entity
              rendered: false
              rendered_strip: false
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_builder: auctions
          entity_relationship:
            entity_type_key: _source.entity
            entity_id_key: _source.id
      relationships: {  }
      css_class: no-sidebar
      use_ajax: true
      use_more: true
      use_more_always: true
      use_more_text: 'All auctions'
      link_display: custom_url
      link_url: /auction
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
      tags: {  }
  embed:
    id: embed
    display_title: Embed
    display_plugin: embed
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history:
          enable_history: false
          exclude_args: ''
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
      tags: {  }
  land_lease:
    id: land_lease
    display_title: 'Land lease'
    display_plugin: embed
    position: 2
    display_options:
      filters:
        filter_auction_type:
          id: filter_auction_type
          table: elasticsearch_result
          field: filter_auction_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_type
          operator: in
          value:
            land_lease: land_lease
          group: 1
          exposed: false
          expose:
            operator_id: filter_auction_type_op
            label: ''
            description: ''
            use_operator: false
            operator: filter_auction_type_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_auction_type
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_auction_status:
          id: filter_auction_status
          table: elasticsearch_result
          field: filter_auction_status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_status
          operator: in
          value:
            active: active
            canceled: canceled
            finished: finished
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        use_more: false
        use_more_always: false
        use_more_text: false
        link_display: false
        link_url: false
        filters: false
        filter_groups: false
      display_description: ''
      use_more: false
      use_more_always: true
      use_more_text: 'All auctions'
      link_display: '0'
      link_url: /auction
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
      tags: {  }
  land_lease_selected:
    id: land_lease_selected
    display_title: 'Land lease selected'
    display_plugin: embed
    position: 1
    display_options:
      filters:
        filter_auction_type:
          id: filter_auction_type
          table: elasticsearch_result
          field: filter_auction_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_type
          operator: in
          value:
            land_lease: land_lease
          group: 1
          exposed: true
          expose:
            operator_id: filter_auction_type_op
            label: ''
            description: ''
            use_operator: false
            operator: filter_auction_type_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: filter_auction_type
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        filter_auction_status:
          id: filter_auction_status
          table: elasticsearch_result
          field: filter_auction_status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: filter_auction_status
          operator: in
          value:
            active: active
            canceled: canceled
            finished: finished
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history:
          enable_history: false
          exclude_args: ''
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
      tags: {  }
