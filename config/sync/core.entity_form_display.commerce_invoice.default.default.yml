uuid: 48e50062-3f76-4f14-bbd5-fda4ce5d2c42
langcode: en
status: true
dependencies:
  config:
    - commerce_invoice.commerce_invoice_type.default
    - field.field.commerce_invoice.default.field_warnings
  module:
    - commerce_order
    - inline_entity_form
id: commerce_invoice.default.default
targetEntityType: commerce_invoice
bundle: default
mode: default
content:
  adjustments:
    type: commerce_adjustment_default
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  translation:
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  field_warnings: true
  state: true
