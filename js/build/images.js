!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.CKEditor5=t():(e.CKEditor5=e.CKEditor5||{},e.CKEditor5.images=t())}(self,(()=>(()=>{var e={"ckeditor5/src/core.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/core.js")},"ckeditor5/src/ui.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/ui.js")},"ckeditor5/src/utils.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/utils.js")},"dll-reference CKEditor5.dll":e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(s){var r=t[s];if(void 0!==r)return r.exports;var n=t[s]={exports:{}};return e[s](n,n.exports,i),n.exports}i.d=(e,t)=>{for(var s in t)i.o(t,s)&&!i.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var s={};return(()=>{"use strict";i.d(s,{default:()=>m});var e=i("ckeditor5/src/core.js");class t extends e.Command{constructor(e,t){super(t),this.attributeName=e}refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled&&e.hasAttribute(this.attributeName)?this.value=e.getAttribute(this.attributeName):this.value=!1}execute(e){const t=this.editor,i=t.plugins.get("ImageUtils"),s=t.model,r=i.getClosestSelectedImageElement(s.document.selection);s.change((t=>t.setAttribute(this.attributeName,e,r)))}}class r extends e.Plugin{static get requires(){return["ImageUtils"]}constructor(e){super(e),this._missingImageStyleViewReferences=new Set}init(){const e=this.editor;this.pluginName=Object.getPrototypeOf(this).constructor.mainPluginName,this.viewAttributeName=e.config.get(`${this.pluginName}.viewAttributeName`),this._defineSchema(),this._defineConverters(),e.commands.add(this.pluginName,new t(this.viewAttributeName,this.editor))}_defineSchema(){const{editor:e}=this,{schema:t}=e.model;t.isRegistered("imageInline")&&t.extend("imageInline",{allowAttributes:[this.viewAttributeName]}),t.isRegistered("imageBlock")&&t.extend("imageBlock",{allowAttributes:[this.viewAttributeName]})}_modelResponsiveImageStyleToDataAttribute(){const e=this.viewAttributeName;function t(t,i,s){const{item:r}=i,{writer:n}=s,o=s.mapper.toViewElement(r),a=Array.from(o.getChildren()).find((e=>"img"===e.name));n.setAttribute(e,i.attributeNewValue,a||o)}return i=>{i.on("attribute:"+e,t,{priority:"high"})}}_defineConverters(){const{editor:e}=this,{conversion:t}=this.editor;t.for("upcast").attributeToAttribute({model:this.viewAttributeName,view:this.viewAttributeName}),t.for("dataDowncast").attributeToAttribute({model:this.viewAttributeName,view:this.viewAttributeName}),t.for("editingDowncast").add(this._imageEditingDowncastConverter("attribute:"+this.viewAttributeName,e)).add(this._imageEditingDowncastConverter("attribute:src",e)).add(this._modelResponsiveImageStyleToDataAttribute()),e.editing.view.on("render",(()=>{for(const e of this._missingImageStyleViewReferences)e.element.isConnected||(e.destroy(),this._missingImageStyleViewReferences.delete(e))}))}_imageEditingDowncastConverter(e){const t=(e,t,i)=>{const s=this.editor;if(!s.plugins.get("ImageUtils").isImage(t.item))return;const r=i.mapper.toViewElement(t.item),n=Array.from(r.getChildren()).find((e=>e.getCustomProperty("drupalImageMissingStyleWarning")));if(t.item.hasAttribute(this.viewAttributeName))return void(n&&i.writer.remove(n));if(n)return;const o=s.ui.componentFactory.create("drupalImageStyleMissing");o.listenTo(s.ui,"update",(()=>{const e=s.model.document.selection.getFirstRange(),i=s.model.createRangeOn(t.item);o.set({isSelected:e.containsRange(i)||e.isIntersecting(i)})})),o.render(),this._missingImageStyleViewReferences.add(o);const a=i.writer.createUIElement("span",{class:"image-style-missing-wrapper"},(function(e){const t=this.toDomElement(e);return t.appendChild(o.element),t}));i.writer.setCustomProperty("drupalImageMissingStyleWarning",!0,a),i.writer.insert(i.writer.createPositionAt(r,"end"),a)};return i=>{i.on(e,t,{priority:"low"})}}}class n extends r{static get pluginName(){return"DrupalImageStyleEditing"}static get mainPluginName(){return"DrupalImageStyle"}}var o=i("ckeditor5/src/ui.js"),a=i("ckeditor5/src/utils.js");class l extends o.View{constructor(e,t){super(t),this.set("isVisible"),this.set("isSelected"),this.setTemplate({tag:"div",attributes:{class:["drupal-image-style-missing",this.bindTemplate.to("isVisible",(e=>e?"":"ck-hidden"))]},children:[e]})}}class c extends e.Plugin{init(){const e=this.editor;this.pluginName=Object.getPrototypeOf(this).constructor.mainPluginName,this.label=e.config.get(`${this.pluginName}.label`),e.ui.componentFactory.add(this.pluginName,(t=>{const i=(0,o.createDropdown)(t);i.buttonView.set({isOn:!1,withText:!0,tooltip:Drupal.t(this.label)});const s=e.commands.get(this.pluginName);i.bind("isEnabled").to(s,"isEnabled");const r=this.editor.config.get(this.pluginName).enabledStyles;i.buttonView.bind("label").to(s,"value",(e=>e?r[e]:Drupal.t("Raw image")));const n=new a.Collection;for(const[e,t]of Object.entries(r)){const i={type:"button",model:new o.ViewModel({withText:!0,label:t,machineName:e})};n.add(i)}return(0,o.addListToDropdown)(i,n),1===Object.keys(r).length&&(this.listenTo(i,"render",(t=>{e.execute(this.pluginName,Object.keys(r)[0])})),this.listenTo(i,"change:isEnabled",((t,i,s,n)=>{!0===s&&e.execute(this.pluginName,Object.keys(r)[0])}))),this.listenTo(i,"execute",(t=>{e.execute(this.pluginName,t.source.machineName),e.editing.view.focus()})),i})),this.editor.ui.componentFactory.add("drupalImageStyleMissing",(e=>{const t=new l(Drupal.t("Please select an image style."),e);return t.listenTo(this.editor.ui,"update",(()=>{t.set({isVisible:!this._isVisible||!t.isSelected})})),t}))}}class d extends c{static get pluginName(){return"DrupalImageStyleUI"}static get mainPluginName(){return"DrupalImageStyle"}}class u extends e.Plugin{static get requires(){return[n,d]}}const m={DrupalImageStyle:u}})(),s=s.default})()));