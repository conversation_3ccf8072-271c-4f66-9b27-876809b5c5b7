<?php

/**
 * @file
 * Add to calendar module.
 */

use Dr<PERSON>al\Core\Entity\EntityInterface;
use Dr<PERSON>al\Core\Entity\Display\EntityViewDisplayInterface;
use Dr<PERSON>al\Core\Datetime\DrupalDateTime;
use <PERSON><PERSON><PERSON>\Core\Url;

/**
 * Implements hook_entity_extra_field_info().
 */
function add_to_calendar_entity_extra_field_info() {
  $extra = [];

  $extra['auction']['stumpage']['display']['add_to_calendar'] = [
    'label' => t('Add to Calendar'),
    'description' => t('Add to calendar button'),
    'weight' => 10,
    'visible' => TRUE,
  ];

  return $extra;
}

/**
 * Implements hook_entity_view().
 */
function add_to_calendar_entity_view(array &$build, EntityInterface $entity, EntityViewDisplayInterface $display, $view_mode) {
  // Check if the "Add to Calendar" component is enabled and end time exists.
  if ($display->getComponent('add_to_calendar') && $entity->hasField('end_time') && !$entity->get('end_time')->isEmpty() && $entity->isActive()) {
    // Gather event details.
    $title = t('Mezabirza.lv auction: @title', [
      '@title' => $entity->label(),
    ]);
    $end_time = new DrupalDateTime($entity->get('end_time')->value);
    $start_time = clone $end_time;
    $start_time->modify('-15 minutes');
    $end_time->modify('+15 minutes');
    $description = t('Link to auction: @url', [
      '@url' => $entity->toLink($entity->label())->toString(),
    ]);
    $utc_timezone = new \DateTimeZone(\Drupal::config('system.date')->get('timezone.default'));
    $start_time->setTimezone($utc_timezone);
    $end_time->setTimezone($utc_timezone);
    $start_formatted = $start_time->format('Ymd\THis\Z');
    $end_formatted = $end_time->format('Ymd\THis\Z');

    // Generate direct calendar links.
    $google_link = add_to_calendar_generate_google_calendar_link($title, $start_formatted, $end_formatted, $description);
    $outlook_link = add_to_calendar_generate_outlook_calendar_link($title, $start_formatted, $end_formatted, $description);

    // Generate ICS content.
    $ics_content = add_to_calendar_generate_ics_content($title, $start_formatted, $end_formatted, $description);
    $ics_link = 'data:text/calendar;charset=utf8;base64,' . base64_encode($ics_content);

    // Add to the render array.
    $build['add_to_calendar'] = [
      '#theme' => 'add_to_calendar_links',
      '#google_link' => Url::fromUri($google_link)->toString(),
      '#google_icon' => '/' . \Drupal::service('extension.path.resolver')->getPath('module', 'add_to_calendar') . '/assets/google_icon.svg',
      '#outlook_link' => Url::fromUri($outlook_link)->toString(),
      '#outlook_icon' => '/' . \Drupal::service('extension.path.resolver')->getPath('module', 'add_to_calendar') . '/assets/outlook_icon.svg',
      '#ics_link' => $ics_link,
      '#ics_icon' => '/' . \Drupal::service('extension.path.resolver')->getPath('module', 'add_to_calendar') . '/assets/ical_icon.svg',
      '#attached' => ['library' => ['add_to_calendar/add_to_calendar']],
      '#cache' => ['contexts' => ['user']],
    ];
  }
}

/**
 * Generate ICS calendar data.
 *
 * @param string $title
 *   The title of the event.
 * @param string $start_formatted
 *   The formatted start time.
 * @param string $end_formatted
 *   The formatted end time.
 * @param string $description
 *   The description of the event.
 *
 * @return string
 *   The ICS calendar data.
 */
function add_to_calendar_generate_ics_content($title, $start_formatted, $end_formatted, $description) {
  $uid = uniqid();
  $now = gmdate('Ymd\THis\Z');

  $ics = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Mezabirza//NONSGML Add to Calendar//EN',
    'METHOD:PUBLISH',
    'BEGIN:VEVENT',
    'UID:' . $uid,
    'DTSTAMP:' . $now,
    'DTSTART:' . $start_formatted,
    'DTEND:' . $end_formatted,
    'SUMMARY:' . $title,
    'DESCRIPTION:' . str_replace("\n", "\\n", $description),
    'END:VEVENT',
    'END:VCALENDAR',
  ];

  return implode("\r\n", $ics);
}

/**
 * Generate Google Calendar link.
 *
 * @param string $title
 *   The title of the event.
 * @param string $start_time
 *   The start time of the event.
 * @param string $end_time
 *   The end time of the event.
 * @param string $description
 *   The description of the event.
 *
 * @return string
 *   The Google Calendar link.
 */
function add_to_calendar_generate_google_calendar_link($title, $start_time, $end_time, $description) {
  $dates = $start_time . '/' . $end_time;

  // Encode parameters to handle special characters.
  $title = urlencode($title);
  $description = urlencode($description);

  // Construct the Google Calendar URL.
  return "https://www.google.com/calendar/render?action=TEMPLATE&text={$title}&dates={$dates}&details={$description}";
}

/**
 * Generate Outlook Calendar link.
 *
 * @param string $title
 *   The title of the event.
 * @param string $start_time
 *   The start time of the event.
 * @param string $end_time
 *   The end time of the event.
 * @param string $description
 *   The description of the event.
 *
 * @return string
 *   The Outlook Calendar link.
 */
function add_to_calendar_generate_outlook_calendar_link($title, $start_time, $end_time, $description) {
  // Encode parameters.
  $title = urlencode($title);
  $description = urlencode($description);

  // Construct the Outlook URL.
  return "https://outlook.live.com/owa/?path=/calendar/action/compose&subject={$title}&startdt={$start_time}&enddt={$end_time}&body={$description}";
}

/**
 * Implements hook_theme().
 */
function add_to_calendar_theme($existing, $type, $theme, $path) {
  return [
    'add_to_calendar_links' => [
      'variables' => [
        'google_link' => NULL,
        'google_icon' => NULL,
        'outlook_link' => NULL,
        'outlook_icon' => NULL,
        'ics_link' => NULL,
        'ics_icon' => NULL,
      ],
      'template' => 'add-to-calendar-links',
    ],
  ];
}
