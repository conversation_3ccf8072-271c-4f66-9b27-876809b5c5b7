<?php

namespace <PERSON><PERSON>al\mezabirza\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\user\Entity\User;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Returns responses for Mežabirža routes.
 */
class MezabirzaUserOptOutController extends ControllerBase {

  /**
   * Builds the response.
   */
  public function build(User $user, $token) {

    if (hash('ripemd160', $user->id() . '?auction2 mod!') != $token) {
      throw new NotFoundHttpException();
    }

    $user->set('field_status', 'user_cancel_confirmed')->save();
    $build['content'] = [
      '#type' => 'html_tag',
      '#tag' => 'h3',
      '#value' => $this->t('Your account will be deleted in 24 hours.'),
      '#attributes' => [
        'class' => ['card-white'],
      ],
    ];

    return $build;
  }

}
