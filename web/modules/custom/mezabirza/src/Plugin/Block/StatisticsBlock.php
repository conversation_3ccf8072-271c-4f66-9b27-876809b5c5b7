<?php

namespace <PERSON><PERSON>al\mezabirza\Plugin\Block;

use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Database\Connection;
use Dr<PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON><PERSON>\Core\Site\Settings;
use <PERSON><PERSON><PERSON>\auctions\AuctionInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a statistics block.
 *
 * @Block(
 *   id = "mezabirza_statistics",
 *   admin_label = @Translation("Statistics"),
 *   category = @Translation("Custom")
 * )
 */
class StatisticsBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  protected $connection;

  /**
   * Constructs a new StatisticsBlock instance.
   *
   * @param array $configuration
   *   The plugin configuration, i.e. an array with configuration values keyed
   *   by configuration option name. The special key 'context' may be used to
   *   initialize the defined contexts by setting it to an array of context
   *   values keyed by context names.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   */
  public function __construct(array $configuration, $plugin_id, $plugin_definition, Connection $connection) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->connection = $connection;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('database')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $users = $this->connection->select('users_field_data', 'u')
      ->countQuery()
      ->execute()
      ->fetchField();
    $auctions = $this->connection->select('auction_field_data', 'a')
      ->condition('status', [
        AuctionInterface::FINISHED,
        AuctionInterface::ACTIVE,
      ], 'IN')
      ->countQuery()
      ->execute()
      ->fetchField();
    $bids = $this->connection->select('bid', 'b')
      ->countQuery()
      ->execute()
      ->fetchField();
    $build['users'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $this->t('@users registered users', [
        '@users' => $users,
      ]),
      '#cache' => [
        'tags' => ['user_list'],
      ],
    ];
    $build['auctions'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $this->t('@bids bids in @auctions auctions', [
        '@auctions' => $auctions,
        '@bids' => $bids,
      ]),
      '#cache' => [
        'tags' => ['bid_list', 'auction_list'],
      ],
    ];
    $build['online'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => '',
      '#attributes' => [
        'id' => 'users-online',
      ],
    ];

    $build['#attached']['drupalSettings']['push_env'] = Settings::get('push_env');
    $build['#attached']['library'][] = 'mezabirza/dynamic_statistic';

    return $build;
  }

}
