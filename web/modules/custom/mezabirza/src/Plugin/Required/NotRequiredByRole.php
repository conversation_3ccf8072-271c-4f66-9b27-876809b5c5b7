<?php

namespace Dr<PERSON>al\mezabirza\Plugin\Required;

use <PERSON>upal\Core\Field\FieldDefinitionInterface;
use <PERSON>upal\Core\Session\AccountInterface;
use <PERSON>upal\required_by_role\Plugin\Required\RequiredByRole;

/**
 * Not Required by role plugin.
 *
 * @Required(
 *   id = "not_required_by_role",
 *   admin_label = @Translation("NOT Required by role"),
 *   label = @Translation("NOT Required by role"),
 *   description = @Translation("Not Required based on current user roles.")
 * )
 */
class NotRequiredByRole extends RequiredByRole {

  /**
   * {@inheritdoc}
   */
  public function isRequired(FieldDefinitionInterface $field, AccountInterface $account) {
    return !parent::isRequired($field, $account);
  }

}
