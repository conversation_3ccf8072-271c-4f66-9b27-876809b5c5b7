<?php

namespace Drupal\mezabirza\Plugin\Field\FieldFormatter;

use <PERSON>upal\Core\Field\FieldItemListInterface;
use <PERSON>upal\Core\Form\FormStateInterface;
use Drupal\commerce_invoice\Plugin\Field\FieldFormatter\InvoiceTotalSummary;
use Drupal\commerce_price\TwigExtension\PriceTwigExtension;

/**
 * Plugin implementation of the 'commerce_invoice_total_summary' formatter.
 *
 * @FieldFormatter(
 *   id = "mezabirzae_total_invoice_price",
 *   label = @Translation("Invoice price"),
 *   field_types = {
 *     "commerce_price",
 *   },
 * )
 */
class InvoicePrice extends InvoiceTotalSummary {

  /**
   * {@inheritdoc}
   */
  public static function defaultSettings() {
    return [
      'attribute' => 'price',
    ] + parent::defaultSettings();
  }

  /**
   * {@inheritdoc}
   */
  public function settingsForm(array $form, FormStateInterface $form_state) {
    return [
      'attribute' => [
        '#type' => 'select',
        '#title' => $this->t('Attribute to display'),
        '#options' => [
          'price' => $this->t('Price'),
          'tax' => $this->t('Tax'),
          'total' => $this->t('Total'),
        ],
        '#default_value' => $this->getSetting('attribute'),
      ],
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    /** @var \Drupal\commerce_invoice\Entity\InvoiceInterface $invoice */
    $invoice = $items->getEntity();
    $elements = [];
    $options = ['currency_display' => 'none'];
    if (!$items->isEmpty()) {
      $totals = $this->invoiceTotalSummary->buildTotals($invoice);
      if ($this->getSetting('attribute') == 'price') {
        $subtotal = $totals['subtotal'];
        foreach ($totals['adjustments'] as $adjustment) {
          if ($adjustment['type'] == 'promotion') {
            $subtotal = $subtotal->add($adjustment['amount']);
          }
        }
        $elements[0] = [
          '#markup' => PriceTwigExtension::formatPrice($subtotal, $options),
        ];
      }
      if ($this->getSetting('attribute') == 'tax') {
        foreach ($totals['adjustments'] as $adjustment) {
          if ($adjustment['type'] == 'tax') {
            $elements[0] = [
              '#markup' => PriceTwigExtension::formatPrice($adjustment['amount'], $options),
            ];
          }
        }
      }
      if ($this->getSetting('attribute') == 'total') {
        $elements[0] = [
          '#markup' => PriceTwigExtension::formatPrice($totals['total'], $options),
        ];
      }
    }

    return $elements;
  }

}
