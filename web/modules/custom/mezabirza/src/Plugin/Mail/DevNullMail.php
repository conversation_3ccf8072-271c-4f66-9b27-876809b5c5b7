<?php

namespace Dr<PERSON>al\mezabirza\Plugin\Mail;

use <PERSON>upal\Core\Mail\MailInterface;
use <PERSON>upal\Core\Mail\Plugin\Mail\PhpMail;

/**
 * Defines a mail sending implementation that does not send mails.
 *
 * @Mail(
 *   id = "dev_null_mail",
 *   label = @Translation("Dev null"),
 *   description = @Translation("Avoid sending mail")
 * )
 */
class DevNullMail extends PhpMail implements MailInterface {

  /**
   * {@inheritdoc}
   */
  public function mail(array $message) {
    return TRUE;
  }

}
