<?php

namespace Drupal\mezabirza;

use Drupal\Core\Field\FieldItemList;
use Drupal\Core\TypedData\ComputedItemListTrait;

/**
 * Defines a computed field for the number of auctions a user has created.
 */
class AuctionsCreatedFieldItemList extends FieldItemList {
  use ComputedItemListTrait;

  /**
   * {@inheritdoc}
   */
  protected function computeValue() {
    // Load the user entity.
    $user = $this->getEntity();

    // Query the 'auction' entity type for entities authored by the user.
    $query = \Drupal::entityQuery('auction')
      ->accessCheck(FALSE)
      ->condition('uid', $user->id());
    $result = $query->execute();

    // Count the number of results.
    $count = count($result);

    // Set the computed value for the field item list.
    $this->list[0] = $this->createItem(0, $count);
  }

}
