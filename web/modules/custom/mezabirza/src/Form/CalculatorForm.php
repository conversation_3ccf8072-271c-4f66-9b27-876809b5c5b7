<?php

namespace Drupal\mezabirza\Form;

use Drupal\Core\Entity\EntityFieldManagerInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\auctions\AuctionElasticPriceCollector;
use Drupal\message\Entity\Message;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Drupal\Core\Config\ConfigFactoryInterface;

/**
 * Provides a Mežabirža form.
 */
class CalculatorForm extends FormBase {

  /**
   * Language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  private LanguageManagerInterface $languageManager;

  /**
   * Language code.
   *
   * @var string
   */
  private string $langcode;

  /**
   * Entity field manager.
   *
   * @var \Drupal\Core\Entity\EntityFieldManagerInterface
   */
  private EntityFieldManagerInterface $entityFieldManager;

  /**
   * Price collector.
   *
   * @var \Drupal\auctions\AuctionElasticPriceCollector
   */
  private AuctionElasticPriceCollector $auctionPriceCollector;

  /**
   * Entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * Session.
   *
   * @var \Symfony\Component\HttpFoundation\Session\SessionInterface
   */
  private SessionInterface $session;

  /**
   * Config factory.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * Price groups.
   *
   * @var array
   */
  private array $priceGroups;

  /**
   * Construct calculator form.
   *
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   Language manager.
   * @param \Drupal\Core\Entity\EntityFieldManagerInterface $entity_field_manager
   *   Entity field manager.
   * @param \Drupal\auctions\AuctionElasticPriceCollector $auction_price_collector
   *   Price collector.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   Entity type manager.
   * @param \Symfony\Component\HttpFoundation\Session\SessionInterface $session
   *   Session.
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   Config factory.
   */
  public function __construct(
    LanguageManagerInterface $language_manager,
    EntityFieldManagerInterface $entity_field_manager,
    AuctionElasticPriceCollector $auction_price_collector,
    EntityTypeManagerInterface $entity_type_manager,
    SessionInterface $session,
    ConfigFactoryInterface $config_factory,
  ) {
    $this->languageManager = $language_manager;
    $this->entityFieldManager = $entity_field_manager;
    $this->auctionPriceCollector = $auction_price_collector;
    $this->entityTypeManager = $entity_type_manager;
    $this->session = $session;
    $this->configFactory = $config_factory;
    $this->langcode = $language_manager->getCurrentLanguage()->getId();
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container): CalculatorForm|static {
    return new static(
      $container->get('language_manager'),
      $container->get('entity_field.manager'),
      $container->get('auction_elastic_pricepoint'),
      $container->get('entity_type.manager'),
      $container->get('session'),
      $container->get('config.factory')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'mezabirza_calculator';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    if ($this->langcode == $this->languageManager->getDefaultLanguage()->getId()) {
      $this->priceGroups = $this->entityFieldManager
        ->getFieldDefinitions('auction', 'stumpage')['field_price_group']
        ->getSetting('allowed_values');
    }
    else {
      $this->priceGroups = $this->languageManager
        ->getLanguageConfigOverride($this->langcode, 'field.storage.auction.field_price_group')
        ->get('settings')['allowed_values'];
      foreach ($this->priceGroups as $key => $value) {
        $this->priceGroups[$key] = $value['label'];
      }
    }
    // Remove option other.
    unset($this->priceGroups[4]);
    // Prepend option all.
    $this->priceGroups = [-1 => t('All')] + $this->priceGroups;
    $form['#attributes'] = [
      'class' => [
        'mezabirza-calculator',
      ],
    ];
    $form['input'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['calculator-form-input'],
      ],
    ];
    // Add description text.
    $form['input']['description'] = [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#value' => t('Enter the felling cadastre no. or mark the cutting location point on the map.'),
      '#attributes' => [
        'class' => ['calculator-form-description'],
      ],
    ];
    $form['input']['cadaster'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['calculator-form-input-cadaster'],
      ],
    ];

    // Cadastral number.
    $form['input']['cadaster']['cadastral_number'] = [
      '#type' => 'textfield',
      '#title' => t('Cadastral number'),
      '#size' => 120,
      '#default_value' => $form_state->getValue('cadastral_number') ?? '',
    ];
    // Button to put cadastral number to the map.
    $form['input']['cadaster']['cadastral_number_button'] = [
      '#type' => 'button',
      '#value' => t('Find on map'),
      '#attributes' => [
        'class' => ['button', 'button--primary'],
      ],
    ];
    // Add error message area.
    $form['input']['error_message'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => '',
      '#attributes' => [
        'class' => ['error-message'],
      ],
    ];

    $form['input']['geofield'] = [
      '#type' => 'geofield_map',
      '#error_label' => t('Map'),
      '#hide_geocode_address' => TRUE,
      '#hide_coordinates' => TRUE,
      '#map_library' => 'leaflet',
      '#zoom' => [
        'start' => 7,
        'min' => 1,
        'max' => 18,
        'focus' => 7,
      ],
      '#gmap_places' => FALSE,
      '#gmap_places_options' => [],
      '#map_type' => 'OpenStreetMap_Mapnik',
      '#map_type_selector' => FALSE,
      '#map_types_google' => 'roadmap',
      '#map_types_leaflet' => [
        'OpenStreetMap_Mapnik' => [
          'label' => 'OpenStreetMap',
          'url' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          'options' => [
            'maxZoom' => 19,
            'attribution' => 'Map data © <a href="https://openstreetmap.org">OpenStreetMap</a> contributors',
          ],
        ],
      ],
      '#click_to_find_marker' => FALSE,
      '#click_to_place_marker' => FALSE,
      '#click_to_remove_marker' => FALSE,
      '#geolocation' => FALSE,
      '#gmap_api_key' => '123',
      '#default_value' => [
        'lat' => '56.955436',
        'lon' => '24.241333',
      ],
    ];

    $form['input']['price_group'] = [
      '#type' => 'select',
      '#title' => t('Price group'),
      '#options' => $this->priceGroups,
      '#default_value' => $form_state->getValue('price_group') ?? -1,
      '#required' => TRUE,
    ];

    $form['input']['volume'] = [
      '#type' => 'number',
      '#title' => t('Volume m³'),
      '#size' => 120,
      '#min' => 50,
      '#max' => 9999,
      '#required' => TRUE,
      '#default_value' => $form_state->getValue('volume') ?? 500,
    ];
    $config = $this->configFactory->get('mezabirza.settings');

    $form['actions'] = [
      '#type' => 'actions',
      '#weight' => 100,
      'data' => [
        '#type' => 'container',
      ],
      'submit' => [
        '#type' => 'submit',
        '#value' => $this->t('Calculate'),
        '#ajax' => [
          'callback' => [$this, 'ajaxCallback'],
          'wrapper' => 'calculation-summary',
          'effect' => 'fade',
          'event' => 'click',
          'progress' => [
            'type' => 'none',
          ],
        ],
      ],
    ];
    $form['note'] = [
      '#type' => 'html_tag',
      '#tag' => 'small',
      '#weight' => 101,
      '#value' => $config->get('calculator_form_note'),
      '#access' => !empty($config->get('calculator_form_note')),
      '#attributes' => [
        'class' => 'calculator-form-note',
      ],
    ];

    // Add empty result container.
    $form['results'] = [
      '#type' => 'container',
      '#attributes' => [
        'id' => 'calculation-summary',
      ],
      '#weight' => 102,
    ];
    $data = $this->session->get('calculator_data');
    if (isset($data['cadastral_number'])) {
      $form['input']['cadaster']['cadastral_number']['#value'] = $data['cadastral_number'];
    }
    if (!empty($data['geofield'])) {
      $form['input']['geofield']['#default_value'] = $data['geofield'];
    }
    if (isset($data['price_group'])) {
      $form['input']['price_group']['#value'] = $data['price_group'];
    }
    if (isset($data['volume'])) {
      $form['input']['volume']['#value'] = $data['volume'];
    }
    $this->session->remove('calculator_data');

    $form['#attached']['library'][] = 'mezabirza/calculator_form';

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $form_state->clearErrors();
  }

  /**
   * Ajax callback for calculator form.
   *
   * @param array $form
   *   The form.
   * @param \Drupal\Core\Form\FormStateInterface $form_state
   *   The form state.
   *
   * @return mixed
   *   The form element.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  public function ajaxCallback(array &$form, FormStateInterface $form_state) {
    $geofield = $form_state->getValue('geofield');
    $volume = (int) $form_state->getValue('volume');
    $priceCollection = $this->auctionPriceCollector
      ->getStumpPriceCollection($form_state->getUserInput()['price_group'], (int) $form_state->getValue('volume'), $geofield['lat'], $geofield['lon']);
    if ($priceCollection->getAverage()) {
      $form['results']['card_wrapper'] = [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['calculator-form-card-wrapper'],
        ],
      ];
      $form['results']['card_wrapper']['average_price'] = [
        '#theme' => 'mb_calculator_result_card',
        '#title' => $this->t('Average price'),
        '#price' => number_format($priceCollection->getAverage() * $volume, 2, '.', ' '),
        '#m3price' => number_format($priceCollection->getAverage(), 2, '.', ' '),
        '#volume' => $form_state->getValue('volume'),
      ];
      $form['results']['card_wrapper']['max_price'] = [
        '#theme' => 'mb_calculator_result_card',
        '#title' => $this->t('Maximum price'),
        '#subtitle' => $this->t('Average price'),
        '#price' => number_format($priceCollection->getMaximum() * $volume, 2, '.', ' '),
        '#m3price' => number_format($priceCollection->getMaximum(), 2, '.', ' '),
        '#volume' => $form_state->getValue('volume'),
      ];
      $items = [];
      $view_builder = $this->entityTypeManager->getViewBuilder('auction');
      foreach ($priceCollection->getAuctions() as $auction) {
        $items[] = $view_builder->view($auction, 'teaser');
      }
      $form['results']['list'] = [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['calculator-form-results'],
        ],
        'auctions' => [
          '#theme' => 'auction-grid',
          '#label' => $this->t('Result calculated from these auctions'),
          '#items' => $items,
        ],
      ];
    }
    else {
      $form['results']['no_results'] = [
        '#type' => 'html_tag',
        '#tag' => 'div',
        '#value' => $this->t('It was not possible to calculate price.'),
      ];
    }

    if (!empty($form_state->getValue('cadastral_number')) && !$form_state->isExecuted()) {
      Message::create([
        'template' => 'calculator_search',
        'uid' => \Drupal::currentUser()->id(),
        'arguments' => [
          '@subject' => $form_state->getValue('cadastral_number'),
        ],
      ])->save();
    }

    // Save cadaster number to session.
    $this->session->set('calculator_data', [
      'cadastral_number' => $form_state->getValue('cadastral_number') ?? 'N/A',
      'volume' => $form_state->getValue('volume'),
      'price_group' => isset($this->priceGroups[$form_state->getValue('price_group')])
        ? (is_object($this->priceGroups[$form_state->getValue('price_group')]) && method_exists($this->priceGroups[$form_state->getValue('price_group')], '__toString')
            ? (string) $this->priceGroups[$form_state->getValue('price_group')]
            : $this->priceGroups[$form_state->getValue('price_group')])
        : 'N/A',
      'geofield' => $geofield,
    ]);

    $geojson = [
      'type' => 'FeatureCollection',
      'features' => [
        [
          'type' => 'Feature',
          'geometry' => [
            'type' => 'Point',
            'coordinates' => [
              $geofield['lon'],
              $geofield['lat'],
            ],
          ],
        ],
      ],
    ];

    $context = [
      'volume' => $form_state->getValue('volume'),
      'price_group' => $this->priceGroups[$form_state->getValue('price_group')],
      'region' => 'Unknown',
      'municipality' => 'Unknown',
      'parish' => 'Unknown',
      'role' => $this->currentUser()->isAnonymous() ? 'anonymous' : 'authenticated',
    ];

    $region_id = \Drupal::service('elasticsearch_handler.region_search')->searchRegions(json_encode($geojson));
    if ($region_id) {
      $regions = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadAllParents($region_id);
      $regions = array_reverse($regions);
      $regions = array_map(function ($term) {
        return $term->getName();
      }, $regions);
      $context['region'] = !empty($regions) ? array_shift($regions) : $context['region'];
      $context['municipality'] = !empty($regions) ? array_shift($regions) : $context['municipality'];
      $context['parish'] = !empty($regions) ? array_shift($regions) : $context['parish'];
    }

    \Drupal::service('google_tag.event_collector')->addEvent('price_calculation', $context);

    return $form['results'];
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // Do nothing.
  }

}
