<?php

namespace Drupal\mezabirza\Form;

use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Mail\MailManagerInterface;
use Dr<PERSON>al\user\UserStorageInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Session\Session;

/**
 * Provides a Mežabirža form.
 */
class ContactForm extends FormBase {

  /**
   * The mail manager service.
   *
   * @var \Drupal\Core\Mail\MailManagerInterface
   */
  protected MailManagerInterface $mailManager;

  /**
   * The session service.
   *
   * @var \Symfony\Component\HttpFoundation\Session\Session
   */
  protected Session $session;

  /**
   * The user storage service.
   *
   * @var \Drupal\user\UserStorageInterface
   */
  protected UserStorageInterface $userStorage;

  /**
   * Constructs a new ContactForm.
   *
   * @param \Drupal\Core\Mail\MailManagerInterface $mail_manager
   *   The mail manager service.
   * @param \Symfony\Component\HttpFoundation\Session\Session $session
   *   The session manager service.
   * @param \Drupal\user\UserStorageInterface $user_storage
   *   The user storage service.
   */
  public function __construct(MailManagerInterface $mail_manager, Session $session, UserStorageInterface $user_storage) {
    $this->mailManager = $mail_manager;
    $this->session = $session;
    $this->userStorage = $user_storage;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container): ContactForm|static {
    return new static(
      $container->get('plugin.manager.mail'),
      $container->get('session'),
      $container->get('entity_type.manager')->getStorage('user')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'mezabirza_contact';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['phone'] = [
      '#type' => 'tel',
      '#title' => $this->t('Phone number'),
      '#required' => TRUE,
      '#placeholder' => $this->t('37112345678'),
      '#attributes' => [
        'autocomplete' => 'tel',
      ],
    ];

    $form['email'] = [
      '#type' => 'email',
      '#title' => $this->t('E-mail'),
      '#placeholder' => $this->t('Enter your e-mail'),
      '#attributes' => [
        'autocomplete' => 'email',
      ],
    ];

    $form['message'] = [
      '#type' => 'textarea',
      '#title' => $this->t('Message'),
      '#attributes' => [
        'maxlength' => 1000,
      ],
      '#required' => TRUE,
      '#placeholder' => $this->t('Enter your message'),
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Send'),
      '#ajax' => [
        'callback' => [$this, 'submitFormAjax'],
        'wrapper' => 'contact-paragraph',
        'effect' => 'fade',
        'progress' => [
          'type' => 'throbber',
        ],
      ],
    ];

    return $form;
  }

  /**
   * Custom submit handler for the contact form.
   *
   * @param array $form
   *   The form array.
   * @param \Drupal\Core\Form\FormStateInterface $form_state
   *   The form state.
   *
   * @return array
   *   The form array.
   */
  public function submitFormAjax(array &$form, FormStateInterface $form_state) {
    $ids = $this->userStorage->getQuery()
      ->condition('status', 1)
      ->accessCheck(FALSE)
      ->condition('roles', 'editor')
      ->execute();

    // Retrieve calculator_data from session.
    $calculator_data = $this->session->get('calculator_data', []);
    // Unset calculator_data from session.
    $this->session->set('calculator_data', []);

    // Prep email parameters.
    $params = [];
    $params['subject'] = $this->t('Contact request.');
    $params['body'][] = $this->t('New contact requested with following information:');
    $params['body'][] = $this->t('Phone: @phone', [
      '@phone' => $form_state->getValue('phone'),
    ]);
    $params['body'][] = $this->t('Email: @email', [
      '@email' => $form_state->getValue('email'),
    ]);
    $params['body'][] = $this->t('Message: @message', [
      '@message' => $form_state->getValue('message'),
    ]);
    // Add each individual variable from calculator_data session value to mail.
    if (!empty($calculator_data)) {
      $params['body'][] = $this->t('Cadastral Number: @cadastral_number', [
        '@cadastral_number' => $calculator_data['cadastral_number'],
      ]);
      $params['body'][] = $this->t('Volume: @volume', [
        '@volume' => $calculator_data['volume'] ?? 'N/A',
      ]);
      $params['body'][] = $this->t('Price Group: @price_group', [
        '@price_group' => $calculator_data['price_group'] ?? 'N/A',
      ]);
      $params['body'][] = $this->t('Geofield: Lat @lat, Lon @lon', [
        '@lat' => $calculator_data['geofield']['lat'] ?? 'N/A',
        '@lon' => $calculator_data['geofield']['lon'] ?? 'N/A',
      ]);
    }

    /** @var \Drupal\user\UserInterface $editor */
    foreach ($this->userStorage->loadMultiple($ids) as $editor) {
      $this->mailManager->mail('auctions', 'contact_us', $editor->getEmail(), $editor->getPreferredLangcode(), $params);
    }

    $title = \Drupal::service('title_resolver')->getTitle(\Drupal::request(), \Drupal::routeMatch()->getCurrentRouteMatch()->getRouteObject());
    // Trigger the contact form submission event.
    $context = [
      'page' => $title,
      'role' => \Drupal::currentUser()->isAnonymous() ? 'anonymous' : 'authenticated',
      'made_calculation' => !empty($calculator_data),
    ];
    \Drupal::service('google_tag.event_collector')->addEvent('contact_form_submission', $context);

    return [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#attributes' => [
        'class' => 'sent-msg',
      ],
      '#value' => $this->t('Thank you for your message! Our customer support staff will contact you as soon as possible.'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // Do nothing.
  }

}
