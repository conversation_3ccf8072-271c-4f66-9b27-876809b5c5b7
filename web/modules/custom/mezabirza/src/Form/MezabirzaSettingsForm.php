<?php

namespace Drupal\mezabirza\Form;

use Dr<PERSON>al\Core\Form\ConfigFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;

/**
 * Provides a Mežabirža admin settings.
 */
class MezabirzaSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      'mezabirza.settings',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'mezabirza_settings_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('mezabirza.settings');

    $form['calculator_form_note'] = [
      '#type' => 'textarea',
      '#title' => $this->t('Display calculator note'),
      '#description' => $this->t('Display calculator additional info note, this is displayed at the bottom'),
      '#default_value' => $config->get('calculator_form_note'),
    ];
    // Add welcome node entity reference field.
    // Get all domains.
    $domains = \Drupal::entityTypeManager()
      ->getStorage('domain')
      ->loadMultiple();
    foreach ($domains as $domain) {
      $welcome_nid = \Drupal::state()->get('mezabirza.settings.welcome_node_' . $domain->id());
      $welcome_nid_buyer = \Drupal::state()->get('mezabirza.settings.welcome_node_buyer_' . $domain->id());
      $form['welcome_node_' . $domain->id()] = [
        '#type' => 'entity_autocomplete',
        '#title' => $this->t('Welcome node for @domain', ['@domain' => $domain->label()]),
        '#description' => $this->t('Select the node to display on the front page.'),
        '#target_type' => 'node',
        '#default_value' => $welcome_nid ? Node::load($welcome_nid) : NULL,
      ];
      $form['buy_welcome_node_' . $domain->id()] = [
        '#type' => 'entity_autocomplete',
        '#title' => $this->t('Welcome node for buyer at @domain', ['@domain' => $domain->label()]),
        '#description' => $this->t('Select the node to display on the front page.'),
        '#target_type' => 'node',
        '#default_value' => $welcome_nid_buyer ? Node::load($welcome_nid_buyer) : NULL,
      ];
    }

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);

    $this->config('mezabirza.settings')
      ->set('calculator_form_note', $form_state->getValue('calculator_form_note'))
      ->save();
    // Get all domains.
    $domains = \Drupal::entityTypeManager()
      ->getStorage('domain')
      ->loadMultiple();
    foreach ($domains as $domain) {
      \Drupal::state()->set('mezabirza.settings.welcome_node_' . $domain->id(), $form_state->getValue('welcome_node_' . $domain->id()));
      \Drupal::state()->set('mezabirza.settings.welcome_node_buyer_' . $domain->id(), $form_state->getValue('buy_welcome_node_' . $domain->id()));
    }
  }

}
