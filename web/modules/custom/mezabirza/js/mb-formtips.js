/**
 * @file
 * Auctions formtips.
 */

(function (Drupal, $, once) {

  'use strict';

  /**
   * End time count.
   *
   * @type {{setElementTime: (function(*): number), attach: attach}}
   */
  Drupal.behaviors.deselect = {
    attach: function (context) {
      function hideOnClickOutside(element, description) {
        const outsideClickListener = function (event) {
          const $target = $(event.target);
          // Check if the click is outside of element or its children.
          if (!$(element).is($target) && $(element).has($target).length === 0) {
            description.removeClass('show');
            description.slideUp();
          }
        }

        $(document).on('click', outsideClickListener);
      }
      once('mb-formtips', '.field--name-mb-field.field--has-description', context).forEach(function (element) {
        // Attach click event to .description.
        const description = $(element).find('.description');
        $(element).find('.formtip').on('click', function () {
          description.toggleClass('show');
          if (description.hasClass('show')) {
            description.slideDown();
          }
          else {
            description.slideUp();
          }
          return false;
        });

        // Hide description on click outside.
        hideOnClickOutside(element, description);


      });
    }
  };

} (Drupal, jQuery, once));
