{#
/**
 * @file
 * Theme for a mb field.
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - label: The label for the field.
 * - value: Field value
 * - description: Description of the field.
 *
 */
#}
{%
  set classes = [
  'field',
  'field--name-mb-field',
  'field--type-string',
  'field--label-inline',
  'inline',
  description ? 'field--has-description',
  ]
%}
{%
  set title_classes = [
  'field__label',
]
%}

<div{{ attributes.addClass(classes) }}>
  <div class="inner">
    <div{{ title_attributes.addClass(title_classes) }}>
      {{ label }}
      {% if description %}
        {{ attach_library('mezabirza/mb_formtips') }}
        <a class="formtip"></a>
      {% endif %}
    </div>
    <div class="field__item">
      {{ value_attributes.raw ? (value | raw) : value }}
    </div>
    {% if description %}
      <div class="description" style="display: none">
        <span class="cross-stand-alone" style="display: none;"></span>
        {{ description }}
      </div>
    {% endif %}
  </div>
</div>
