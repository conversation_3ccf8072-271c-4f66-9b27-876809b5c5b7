<?php

/**
 * @file
 * Primary module hooks for Mežabirža module.
 */

use Drupal\Core\Access\AccessResult;
use Drupal\Core\Ajax\AjaxResponse;
use Drupal\Core\Ajax\CloseModalDialogCommand;
use Drupal\Core\Ajax\HtmlCommand;
use Drupal\Core\Ajax\RedirectCommand;
use Drupal\Core\Cache\Cache;
use Drupal\Core\Datetime\DrupalDateTime;
use Drupal\Core\Datetime\Entity\DateFormat;
use Drupal\Core\Entity\Display\EntityViewDisplayInterface;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityTypeInterface;
use Drupal\Core\Field\BaseFieldDefinition;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Render\Element\StatusMessages;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Url;
use Drupal\address\Plugin\Field\FieldWidget\AddressDefaultWidget;
use Drupal\auctions\AuctionInterface;
use Drupal\auctions\Entity\Auction;
use Drupal\message\Entity\Message;
use Drupal\paragraphs\Entity\Paragraph;
use Drupal\simplenews\Entity\Newsletter;
use Drupal\simplenews\Entity\Subscriber;
use Drupal\symfony_mailer\Email;
use Drupal\text\Plugin\Field\FieldWidget\TextareaWidget;
use Drupal\user\Entity\User;
use Drupal\user\UserInterface;
use Drupal\views\ViewExecutable;
use Drupal\views\Views;

/**
 * Implements hook_theme().
 */
function mezabirza_theme() {
  return [
    'card' => [
      'variables' => [
        'title' => NULL,
        'subtitle' => NULL,
        'url' => NULL,
        'content' => NULL,
        'image' => NULL,
        'footer' => NULL,
        'modifiers' => [],
        'attributes' => [],
      ],
    ],
    'mb_field' => [
      'variables' => [
        'label' => NULL,
        'value' => NULL,
        'title_attributes' => [],
        'value_attributes' => [],
        'description' => NULL,
        'attributes' => [],
      ],
    ],
    'mb_field_attachment' => [
      'variables' => [
        'label' => NULL,
        'value' => NULL,
        'title_attributes' => [],
        'value_attributes' => [],
        'attributes' => [],
      ],
    ],
    'mb_calculator_result_card' => [
      'variables' => [
        'title' => NULL,
        'price' => NULL,
        'm3price' => NULL,
        'volume' => NULL,
        'link' => NULL,
      ],
    ],
    'auction-grid' => [
      'variables' => [
        'label' => NULL,
        'items' => NULL,
        'grid__modifiers' => ['three-columns', 'show-more'],
        'show_items' => 3,
      ],
    ],
    'mezabirza_icon_with_text' => [
      'variables' => [
        'text' => NULL,
        'icon' => NULL,
      ],
      'template' => 'icon-with-text',
    ],
    'mezabirza_cta_button' => [
      'variables' => [
        'url' => NULL,
        'text' => NULL,
        'align' => 'left',
      ],
      'template' => 'cta-button',
    ],
  ];
}

/**
 * Implements hook_entity_base_field_info_alter().
 */
function mezabirza_entity_base_field_info_alter(&$fields, $entity_type) {
  // If entity is user show timezone and name.
  if ($entity_type->id() == 'user') {
    $fields['timezone']->setDisplayConfigurable('view', TRUE)
      ->setTargetEntityTypeId('user');
    $fields['name']->setDisplayConfigurable('view', TRUE)
      ->setLabel(t('Username'))
      ->setTargetEntityTypeId('user');
  }
}

/**
 * Implements hook_ENTITY_TYPE_view_alter() for user.
 */
function mezabirza_user_view_alter(array &$build, EntityInterface $entity) {
  if (empty($build['simplenews']['subscriptions'])) {
    return;
  }
  $links = [];
  if ($subscriber = Subscriber::loadByUid($entity->id())) {
    $build['#cache']['tags'] = Cache::mergeTags($build['#cache']['tags'], $subscriber->getCacheTags());
    $build['#cache']['context'] = $entity->getCacheContexts();
    foreach (simplenews_newsletter_get_visible() as $newsletter) {
      if ($subscriber->isSubscribed($newsletter->id())) {
        // @todo Make links
        $links[] = [
          '#type' => 'html_tag',
          '#tag' => 'div',
          '#value' => $newsletter->label(),
          '#attributes' => ['class' => ['field__item']],
        ];
      }
    }
  }

  $build['simplenews'] = [
    '#type' => 'container',
    '#title' => t('Subscriptions'),
    '#attributes' => [
      'class' => [
        'field',
        'field--name-simplenews',
        'field--type-list-string',
        'field--label-inline',
      ],
    ],
    'label' => [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => t('Subscribed'),
      '#attributes' => ['class' => ['field__label']],
    ],
    'items' => [
      '#type' => 'container',
      'subscriptions' => !empty($links) ? $links : [
        '#markup' => t('No subscriptions.'),
        '#attributes' => ['class' => ['field__item']],
      ],
      '#attributes' => ['class' => ['field__items']],
    ],
  ];
}

/**
 * Implements hook_ENTITY_TYPE_view() for auction.
 */
function mezabirza_auction_view(array &$build, AuctionInterface $auction, EntityViewDisplayInterface $display, $view_mode) {
  $session = \Drupal::service('session');
  if ($view_mode == 'full' && $auction->getOwnerId() != \Drupal::currentUser()->id() && $session->get('auction:view:' . $auction->id(), 0) < time() - 60) {
    if (!$auction->get('field_view_count')->value) {
      $auction->set('field_view_count', 1);
      $auction->save();
    }
    else {
      $view_count = (int) $auction->get('field_view_count')->value + 1;
      \Drupal::database()->update('auction__field_view_count')
        ->fields(['field_view_count_value' => $view_count])
        ->condition('entity_id', $auction->id())
        ->execute();
      Cache::invalidateTags($auction->getCacheTagsToInvalidate());
      \Drupal::entityTypeManager()
        ->getStorage('auction')
        ->resetCache([$auction->id()]);
    }
    $session->set('auction:view:' . $auction->id(), time());
  }
}

/**
 * Implements hook_ENTITY_TYPE_view() for paragraph.
 */
function mezabirza_paragraph_view(array &$build, Paragraph $paragraph, EntityViewDisplayInterface $display, $view_mode) {
  if ($paragraph->bundle() == 'calculator') {
    $build['calculator'] = \Drupal::formBuilder()->getForm('Drupal\mezabirza\Form\CalculatorForm');
  }
  if ($paragraph->bundle() == 'short_calculator') {
    $build['short_calculator'] = \Drupal::formBuilder()->getForm('Drupal\mezabirza\Form\ShortCalculatorForm');
    $build['#attributes']['class'][] = 'card-white';
  }
  if ($paragraph->bundle() == 'iin_calculator') {
    $build['calculator'] = \Drupal::formBuilder()->getForm('\Drupal\mezabirza\Form\IinCalculatorForm', $paragraph->get('field_text'));
  }
  if ($paragraph->bundle() == 'contact_us') {
    $build['contact_form'] = \Drupal::formBuilder()->getForm('Drupal\mezabirza\Form\ContactForm');
  }
  if ($paragraph->bundle() == 'count_up') {
    $build['number'] = [
      '#markup' => \Drupal::service('token')->replace($paragraph->get('field_number')->value, [
        'node' => $paragraph->get('field_number')->getEntity()->getParentEntity(),
        'paragraph' => $paragraph,
      ], ['clear' => TRUE]),
    ];
    $build['#attached']['library'][] = 'mezabirza/counter';
  }
  if ($paragraph->bundle() == 'small_auction_teasers') {
    // Check if there are at least 2 active auctions.
    $query = \Drupal::entityQuery('auction')
      ->accessCheck(FALSE)
      ->condition('status', AuctionInterface::ACTIVE)
      ->condition('field_private', 0)
      ->sort('end_time')
      ->range(0, 2);
    $ids = $query->execute();
    // If not just load 2 auctions ordering by end time.
    if (count($ids) < 2) {
      $query = \Drupal::entityQuery('auction')
        ->accessCheck(FALSE)
        ->condition('field_private', 0)
        ->sort('end_time', 'DESC')
        ->range(0, 2);
      $ids = $query->execute();
    }
    foreach (Auction::loadMultiple($ids) as $auction) {
      $build['auctions'][] = \Drupal::entityTypeManager()
        ->getViewBuilder('auction')
        ->view($auction, 'teaser');
    }

  }
  if ($paragraph->bundle() == 'news_topic') {
    // Get news_topic in topical_news display mode.
    $view = Views::getView('news');
    $view->setDisplay('topical_news');
    $view->preExecute();
    $view->filter['filter_keywords']->value = array_column($paragraph->get('field_terms')->getValue(), 'target_id');
    $view->execute();
    $build['news_topic'] = $view->buildRenderable('topical_news');
  }
}

/**
 * Implements hook_entity_type_build().
 */
function mezabirza_entity_type_build(array &$entity_types) {
  /** @var \Drupal\Core\Entity\EntityTypeInterface[] $entity_types */
  foreach (['notifications', 'subscriptions', 'region', 'account', 'locations', 'documents'] as $item) {
    $entity_types['user']->setLinkTemplate('edit-' . $item, '/user/{user}/edit-' . $item);
    $entity_types['user']->setFormClass($item, 'Drupal\user\ProfileForm');
  }
}

/**
 * Implements hook_field_widget_single_element_form_alter().
 */
function mezabirza_field_widget_single_element_form_alter(array &$element, FormStateInterface $form_state, array $context) {
  if ($context['widget'] instanceof TextareaWidget) {
    $element['#after_build'][] = 'mezabirza__allowed_formats_remove_textarea_help';
  }
  if ($context['widget'] instanceof AddressDefaultWidget) {
    $element['#after_build'][] = 'mezabirza__add_address_placeholders';
  }
}

/**
 * After build callback for text widgets.
 *
 * @param array $form_element
 *   Form element.
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 *   Form state.
 *
 * @return array
 *   Element.
 */
function mezabirza__allowed_formats_remove_textarea_help(array $form_element, FormStateInterface $form_state) {
  if (isset($form_element['format'])) {
    // All this stuff is needed to hide the help text.
    unset($form_element['format']['guidelines']);
    unset($form_element['format']['help']);
    unset($form_element['format']['#type']);
    unset($form_element['format']['#theme_wrappers']);
  }

  return $form_element;
}

/**
 * After build callback for Address widgets.
 *
 * @param array $form_element
 *   Form element.
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 *   Form state.
 *
 * @return array
 *   Element.
 */
function mezabirza__add_address_placeholders(array $form_element, FormStateInterface $form_state) {
  if (
    !empty($form_element['address']['postal_code']) &&
    !empty($form_state->getValue('payment_information')['billing_information']['address'][0]['address']['country_code']) &&
    $form_state->getValue('payment_information')['billing_information']['address'][0]['address']['country_code'] == 'LV'
  ) {
    $form_element['address']['postal_code']['#attributes']['placeholder'] = t('LV-1234');
  }

  return $form_element;
}

/**
 * Implements hook_form_alter().
 */
function mezabirza_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  if ($form_id == 'commerce_checkout_flow_multistep_default' && $form['#step_id'] == 'order_information') {
    $form['payment_information']['payment_method']['#weight'] = 100;
  }
  if ($form_id == 'user_notifications_form') {
    unset($form['legal']);
  }
  $form_list = [
    'user_form',
    'simplenews_subscriber_account_form',
    'user_notifications_form',
    'profile_customer_address-book-edit_form',
    'profile_customer_address-book-add_form',
    'customer_edit_form',
  ];
  if (in_array($form_id, $form_list) && \Drupal::request()->isXmlHttpRequest()) {
    $selector = 'ajaxify_submit_form_' . $form_id;
    $form['#prefix'] = '<div id="' . $selector . '">';
    $form['#suffix'] = '</div>';
    foreach (array_keys($form['actions']) as $action) {
      if (!is_array($form['actions'][$action])) {
        continue;
      }
      $form['actions'][$action]['#ajax'] = [
        'callback' => 'mezabirza__ajaxify_submit_forms_form_ajax_callback',
        'wrapper' => $selector,
        'effect' => 'fade',
      ];
    }
    $form['actions']['#weight'] = 10000;
  }
  if (isset($form['from']) || isset($form['kpi']['from'])) {
    $form['date_periods'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => [
          'form-item--no-label',
          'views-exposed-form__item--actions',
          'views-exposed-form__item',
        ],
      ],
      'this_month' => [
        '#type' => 'html_tag',
        '#tag' => 'button',
        '#value' => t('This month'),
        '#attributes' => [
          'id' => 'edit-this-month',
          'type' => 'button',
          'class' => ['button'],
        ],
      ],
      'last_month' => [
        '#type' => 'html_tag',
        '#tag' => 'button',
        '#value' => t('Last month'),
        '#attributes' => [
          'id' => 'edit-last-month',
          'type' => 'button',
          'class' => ['button'],
        ],
      ],
      'this_year' => [
        '#type' => 'html_tag',
        '#tag' => 'button',
        '#value' => t('This year'),
        '#attributes' => [
          'id' => 'edit-this-year',
          'type' => 'button',
          'class' => ['button'],
        ],
      ],
      'last_year' => [
        '#type' => 'html_tag',
        '#tag' => 'button',
        '#value' => t('Last year'),
        '#attributes' => [
          'id' => 'edit-last-year',
          'type' => 'button',
          'class' => ['button'],
        ],
      ],
      '#attached' => [
        'library' => ['mezabirza/period'],
      ],
    ];
    if (isset($form['kpi']['from'])) {
      $form['kpi']['date_periods'] = $form['date_periods'];
      $form['kpi']['date_periods']['#weight'] = -10;
      unset($form['date_periods']);
    }
  }
}

/**
 * Implements hook_form_FORM_ID_alter() for user_form.
 */
function mezabirza_form_user_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  $is_admin_editor = (bool) array_intersect(\Drupal::currentUser()->getRoles(), [
    'editor',
    'administrator',
  ]);
  if (isset($form['field_comments'])) {
    $form['field_comments']['#access'] = $is_admin_editor;
  }
  if (isset($form['field_extra_maps'])) {
    $form['field_extra_maps']['#access'] = $is_admin_editor;
  }
  if (isset($form['field_tags'])) {
    $form['field_tags']['#access'] = $is_admin_editor;
  }
  if (isset($form['field_block_users'])) {
    $form['field_block_users']['#access'] = $is_admin_editor;
  }
  if (isset($form['field_legal_entity'])) {
    $form['field_registration_number']['widget']['0']['value']['#states'] = [
      'visible' => [
        ':input[name="field_legal_entity"]' => ['value' => 'legal'],
      ],
    ];
  }
  // If current path is user edit form then redirect to user profile.
  if (\Drupal::request()->attributes->get('_route') == 'entity.user.edit_form') {
    $form['actions']['submit']['#submit'][] = 'mezabirza__user_form_submit';
  }

  // In documents remove account field so that it does not ask for password.
  if ($form_id == 'user_documents_form') {
    unset($form['account']['current_pass']);
    unset($form['account']['pass']);
  }
}

/**
 * Submit callback for user_form.
 *
 * @param array $form
 *   Form.
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 *   Form state.
 */
function mezabirza__user_form_submit(array $form, FormStateInterface $form_state) {
  $form_state->setRedirect('entity.user.canonical', [
    'user' => $form_state->getFormObject()->getEntity()->id(),
  ]);
}

/**
 * Ajax submit form callback().
 *
 * @param array $form
 *   Form.
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 *   Form state.
 *
 * @return \Drupal\Core\Ajax\AjaxResponse
 *   Response.
 */
function mezabirza__ajaxify_submit_forms_form_ajax_callback(array $form, FormStateInterface $form_state) {
  $messages = StatusMessages::renderMessages(NULL);
  $response = new AjaxResponse();
  $output = [];
  if ($form_state->getErrors()) {
    $output[] = $messages;
    $output[] = $form;
    $response->addCommand(new HtmlCommand(NULL, $output));
  }
  elseif (\Drupal::request()->query->get('destination')) {
    $response->addCommand(new RedirectCommand(\Drupal::request()->query->get('destination')));
  }
  else {
    $response->addCommand(new CloseModalDialogCommand());
  }

  return $response;
}

/**
 * Implements hook_views_pre_render().
 */
function mezabirza_views_pre_render(ViewExecutable $view) {
  if ($view->id() == 'auction_preview') {
    $view->element['#attached']['library'][] = 'mezabirza/exclusive-checkboxes';
  }
}

/**
 * Implements hook_menu_local_tasks_alter().
 */
function mezabirza_menu_local_tasks_alter(&$data, $route_name) {
  $tabs_per_route = [
    'view.transactions.user' => [
      'entity.transaction.user',
      'views_view:view.commerce_user_invoices.invoice_page',
      'userpoints.point_purchase',
    ],
    'view.commerce_user_invoices.invoice_page' => [
      'entity.transaction.user',
      'views_view:view.commerce_user_invoices.invoice_page',
      'userpoints.point_purchase',
    ],
    'view.user_auctions.page' => [
      'views_view:view.user_auctions.page',
      'views_view:view.following_auctions.page',
      'views_view:view.user_auctions_participated.page',
      'views_view:view.user_auctions_participated.auctions_won',
    ],
    'view.following_auctions.page' => [
      'views_view:view.user_auctions.page',
      'views_view:view.following_auctions.page',
      'views_view:view.user_auctions_participated.page',
      'views_view:view.user_auctions_participated.auctions_won',
    ],
    'view.user_auctions_participated.page' => [
      'views_view:view.user_auctions.page',
      'views_view:view.following_auctions.page',
      'views_view:view.user_auctions_participated.page',
      'views_view:view.user_auctions_participated.auctions_won',
    ],
    'view.user_auctions_participated.auctions_won' => [
      'views_view:view.user_auctions.page',
      'views_view:view.following_auctions.page',
      'views_view:view.user_auctions_participated.page',
      'views_view:view.user_auctions_participated.auctions_won',
    ],
  ];
  if (empty($tabs_per_route[$route_name])) {
    return;
  }
  $tabs = $tabs_per_route[$route_name];
  foreach (array_keys($data['tabs'][0]) as $tab) {
    if (!in_array($tab, $tabs)) {
      unset($data['tabs'][0][$tab]);
    }
  }
}

/**
 * Implements hook_entity_operation().
 */
function mezabirza_entity_operation(EntityInterface $entity) {
  // Only show the "Invoices" operation link for orders.
  if ($entity->getEntityTypeId() !== 'commerce_invoice') {
    return;
  }
  // Only show if the user has the "administer commerce_invoice" permission.
  if (!\Drupal::currentUser()->hasPermission('administer commerce_invoice')) {
    return;
  }
  $operations['regenerate'] = [
    'title' => t('Regenerate'),
    'url' => Url::fromRoute('mezabirza.invoice_regenerate', [
      'commerce_invoice' => $entity->id(),
      'destination' => \Drupal::request()->getRequestUri(),
    ]),
    'weight' => 50,
  ];
  return $operations;
}

/**
 * Implements hook_user_format_name_alter().
 */
function mezabirza_user_format_name_alter(&$name, AccountInterface $account) {
  if ($account instanceof User && !$account->get('field_display_name')->isEmpty()) {
    $name = $account->get('field_display_name')->value;
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function mezabirza_form_user_login_form_alter(&$form, FormStateInterface $form_state) {
  $form['#submit'][] = 'mezabirza_user_login_submit';
}

/**
 * Form submission handler for user_login_form().
 *
 * Redirects the user to the frontpage after logging in.
 */
function mezabirza_user_login_submit(&$form, FormStateInterface $form_state) {
  $url = Url::fromRoute('<front>');

  // Check if a destination was set, probably on an exception controller.
  // @see \Drupal\user\Form\UserLoginForm::submitForm()
  $request = \Drupal::service('request_stack')->getCurrentRequest();
  if (!$request->request->has('destination')) {
    $form_state->setRedirectUrl($url);
  }
  else {
    $request->query->set('destination', $request->request->get('destination'));
  }
}

/**
 * Implements hook_commerce_tax_number_type_info_alter().
 */
function mezabirza_commerce_tax_number_type_info_alter(array &$definitions) {
  if (isset($definitions['european_union_vat'])) {
    $definitions['european_union_vat']['examples'] = ['LV44103084337'];
  }
}

/**
 * Implements hook_preprocess_HOOK() for commerce_invoice__admin.
 */
function mezabirza_preprocess_commerce_invoice__admin(array &$variables) {
  /** @var Drupal\commerce_invoice\Entity\InvoiceInterface $invoice */
  $invoice = $variables['elements']['#commerce_invoice'];
  $auction = NULL;
  $comment = NULL;
  foreach ($invoice->getOrders() as $order) {
    if (!$order->get('field_auction')->isEmpty()) {
      $auction = Auction::load($order->get('field_auction')->target_id);
    }
    if (!$order->get('field_comment')->isEmpty()) {
      $comment = $order->get('field_comment')->value;
    }
  }
  if ($auction instanceof AuctionInterface) {
    $variables['invoice']['billing_information']['description'] = [
      '#theme' => 'mb_field',
      '#label' => t('Comments'),
      '#value' => t('Commission fee for auction nr. @id - @label (@date)', [
        '@id' => $auction->id(),
        '@label' => $auction->label(),
        '@date' => $auction->get('end_time')->date->format(DateFormat::load('date_only')->getPattern()),
      ]),
      '#weight' => 200,
    ];
  }
  if (!empty($comment)) {
    $variables['invoice']['billing_information']['comment'] = [
      '#theme' => 'mb_field',
      '#label' => t('Comments'),
      '#value' => $comment,
      '#weight' => 200,
    ];
  }
}

/**
 * Implements hook_ENTITY_TYPE_create() for 'profile' entity.
 */
function mezabirza_profile_create(EntityInterface $entity) {
  if (!$entity->get('field_name')->isEmpty()) {
    return;
  }
  $current_user = User::load(\Drupal::currentUser()->id());
  if ($current_user->get('field_legal_entity')->value != 'legal' || $current_user->get('field_registration_number')->isEmpty()) {
    return;
  }
  if ($data = \Drupal::service('mezabirza.registrationcode')->getInfoFromRegistrationCode($current_user->get('field_registration_number')->value)) {
    $entity->get('address')->address_line1 = $data['street'];
    $entity->get('address')->locality = $data['city'];
    $entity->get('address')->postal_code = $data['index'];
    $entity->get('field_legal_status')->value = 'legal_entity';
    $entity->get('field_name')->value = $data['name'];
    $entity->get('tax_number')->value = 'LV' . $data['regcode'];
    $entity->get('field_registration_number')->value = $data['regcode'];
  }
}

/**
 * Implements hook_ENTITY_TYPE_access() for node.
 */
function mezabirza_node_access(EntityInterface $entity, $operation, AccountInterface $account) {
  if ($operation == 'view' && $entity->bundle() == 'simplenews_issue' && !$account->isAuthenticated()) {
    return AccessResult::forbidden();
  }
}

/**
 * Implements hook_entity_reference_selection_alter().
 */
function mezabirza_entity_reference_selection_alter(array &$info) {
  if (isset($info['default:user'])) {
    $info['default:user']['class'] = '\Drupal\mezabirza\Plugin\EntityReferenceSelection\UserSelection';
  }
}

/**
 * Implements hook_views_pre_build().
 */
function mezabirza_views_pre_build(ViewExecutable $view) {
  if ($view->id() == 'commerce_invoices') {
    $input = $view->getExposedInput();
    if (!empty($input['to'])) {
      $input['to'] = DrupalDateTime::createFromFormat('Y-m-d', $input['to'])->modify('+ 1 day')->format('Y-m-d');
    }
    $view->setExposedInput($input);
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function mezabirza_form_user_register_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  if (\Drupal::config('googlelogin.icon.settings')->get('show_on_login_form')) {
    googlelogin_login_button_code($form);
    // Add Title that explains that You can register using Google account.
    $form['google_oauth_login_title'] = [
      '#type' => 'html_tag',
      '#tag' => 'h2',
      '#value' => t('Register using your Google account.'),
      '#attributes' => ['class' => ['google-oauth-login']],
      '#weight' => -20,
    ];
    $form['google_oauth_login']['#weight'] = -10;
    // Add or element to the form.
    $form['google_oauth_login_or'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => t('or'),
      '#attributes' => ['class' => ['register-or-option']],
      '#weight' => -5,
    ];
    // Add Title that explains that You can register using email.
    $form['email_register_title'] = [
      '#type' => 'html_tag',
      '#tag' => 'h2',
      '#value' => t('Fill in the form below to register.'),
      '#attributes' => ['class' => ['email-register-title']],
      '#weight' => -4,
    ];

  }
}

/**
 * Implements hook_cron().
 */
function mezabirza_cron() {
  // Get previous cron run time.
  $last_run = \Drupal::state()->get('mezabirza.last_cron_run', 0);
  $current_run = \Drupal::time()->getRequestTime();
  if ($last_run + 60 * 60 * 24 > $current_run) {
    return;
  }
  \Drupal::state()->set('mezabirza.last_cron_run', $current_run);
  // Get all users that does not have phone number.
  $uids = \Drupal::entityQuery('user')
    ->condition('status', 1)
    ->notExists('field_phone')
    ->condition('created', $current_run - 48 * 60 * 60, '<')
    ->condition('created', $current_run - 72 * 60 * 60, '>')
    ->accessCheck(FALSE)
    ->execute();
  if (!empty($uids)) {
    foreach (User::loadMultiple($uids) as $user) {
      mezabirza__send_registration_reminder($user);
    }
  }
}

/**
 * Send registration reminder email.
 *
 * @param \Drupal\user\UserInterface $user
 *   User entity.
 */
function mezabirza__send_registration_reminder(UserInterface $user) {
  $params = [
    'user' => $user,
  ];
  $to = $user->getEmail();
  $langcode = $user->language()->getId();
  \Drupal::service('auctions.language_switcher')->changeActiveLanguage($langcode);
  $renderer = \Drupal::service('renderer');
  $params['subject'] = t('Registration reminder', [], ['langcode' => $langcode]);
  $params['body'][] = t('Hi.', [], ['langcode' => $langcode]);
  $params['body'][] = t('Your registration on the mezabirza.lv portal is almost complete!', [], ['langcode' => $langcode]);
  $url = [
    '#type' => 'link',
    '#title' => t('here', [], ['langcode' => $langcode]),
    '#url' => Url::fromUri(user_pass_reset_url($user)),
  ];
  $params['body'][] = t('To complete the registration, click on this link: @url', [
    '@url' => $renderer->renderRoot($url),
  ], ['langcode' => $langcode]);
  $params['body'][] = '<br>';
  $list_title = [
    '#type' => 'html_tag',
    '#tag' => 'h3',
    '#value' => t('After you complete your registration, you will get access to:', [], ['langcode' => $langcode]),
  ];
  $params['body'][] = $renderer->renderRoot($list_title);
  $items[] = t('Place the felling auction on the Meža Exchange portal;', [], ['langcode' => $langcode]);
  $items[] = t('Follow other auctions, the results of which interest you;', [], ['langcode' => $langcode]);
  $items[] = t('Regularly receive news and useful resources in the forest industry.', [], ['langcode' => $langcode]);
  $list = [
    '#theme' => 'item_list',
    '#items' => $items,
  ];
  $params['body'][] = $renderer->renderRoot($list);
  $params['body'][] = t('If you have any questions or need help with the registration process, our team is ready to help!', [], ['langcode' => $langcode]);
  $params['body'][] = t('Contact info: tel. 28684088 , e-mail: <EMAIL>', [], ['langcode' => $langcode]);
  $params['body'][] = '<br>';
  $params['body'][] = '<b>' . t('Our mission is to help forest owners sell their forest at the highest price that loggers are willing to pay in open competition. You will know more - you will get more!', [], ['langcode' => $langcode]) . '</b>';
  \Drupal::service('plugin.manager.mail')->mail('auctions', 'registration_reminder', $to, $langcode, $params, NULL, TRUE);
  // Create message entity.
  Message::create([
    'template' => 'emails',
    'uid' => $user->id(),
    'field_type' => 'registration_reminder',
    'arguments' => [
      '@subject' => $params['subject'],
    ],
  ])->save();
}

/**
 * Implements hook_user_login().
 */
function mezabirza_user_login(UserInterface $account) {
  if ($account->field_status->value == 'warning_sent') {
    $account->field_status->value = 'ok';
    $account->save();
  }
  if ($account->getLastAccessedTime() == 0) {
    // Get current domain.
    $domain = \Drupal::service('domain.negotiator')->getActiveId();
    $destination = \Drupal::state()->get('mezabirza.settings.welcome_node_' . $domain, 534);
    if (!$account->field_interests->isEmpty() && \Drupal::state()->get('mezabirza.settings.welcome_node_buyer_' . $domain)) {
      $destination = \Drupal::state()->get('mezabirza.settings.welcome_node_buyer_' . $domain);
    }
    \Drupal::service('request_stack')
      ->getCurrentRequest()
      ->query
      ->set('destination', Url::fromRoute('entity.node.canonical', ['node' => $destination])->toString());
  }
}

/**
 * Implements hook_page_attachments().
 */
function mezabirza_page_attachments(array &$attachments) {
  $attachments['#attached']['library'][] = 'mezabirza/title-popup';
}

/**
 * Implements hook_mailer_PHASE() for build.
 */
function mezabirza_mailer_build(Email $email) {
  $to = $email->getTo()[0]->getEmail();
  if (empty($to)) {
    return;
  }
  $user = user_load_by_mail($to);
  if (!$user) {
    return;
  }
  $email->setVariable('unsubscribe', Url::fromRoute('mezabirza.mail_subscription', [
    'user' => $user->id(),
    'token' => hash('ripemd160', $user->id() . '?MEZža mod!'),
  ], [
    'absolute' => TRUE,
  ])->toString());
  $email->getHeaders()->remove('list-unsubscribe');
  $email->getHeaders()->addTextHeader('list-unsubscribe', '<' . $email->getVariables()['unsubscribe'] . '>');
}

/**
 * Implements hook_preprocess_HOOK() for auction--full.html.twig.
 */
function mezabirza_preprocess_auction__full(&$variables) {
  $variables['auctions_auction_bids'] = \Drupal::service('mezabirza.block_view_builder')->build('auctions_auction_bids', [
    'limit' => 10,
  ]);
  $auction = $variables['elements']['#auction'];
  if (!$auction->field_private->value) {
    $variables['share_url'] = $auction->toUrl()->setRouteParameter('acct', $auction->getAccessToken())->setAbsolute()->toString();
  }
}

/**
 * Implements hook_entity_base_field_info().
 */
function mezabirza_entity_base_field_info(EntityTypeInterface $entity_type): array {
  $fields = [];
  if ($entity_type->id() === 'user') {
    $fields['auctions_created'] = BaseFieldDefinition::create('integer')
      ->setLabel(t('Number of Auctions Created'))
      ->setDescription(t('The number of auctions this user has created.'))
      ->setComputed(TRUE)
      ->setClass('\Drupal\mezabirza\AuctionsCreatedFieldItemList');

  }
  return $fields;
}

/**
 * Implements hook_views_data().
 */
function mezabirza_views_data() {
  $data = [];

  $data['users_field_data']['auctions_created'] = [
    'title' => t('Number of Auctions Created'),
    'help' => t('The number of auctions this user has created.'),
    'field' => [
      'id' => 'field',
      'field_name' => 'auctions_created',
    ],
  ];

  return $data;
}

/**
 * Implements hook_ENTITY_TYPE_insert() for user.
 */
function mezabirza_user_insert(UserInterface $user) {
  // Check if the current domain is mezabirza.
  // Default to TRUE if domain module is not enabled.
  $is_mezabirza = TRUE;
  if (\Drupal::moduleHandler()->moduleExists('domain')) {
    $domain = \Drupal::service('domain.negotiator')->getActiveId();
    $is_mezabirza = ($domain === 'mezabirza');
    $user->set('field_domain', $domain);
    $user->save();
  }

  // Only proceed with subscription if this is the mezabirza domain.
  if ($is_mezabirza) {
    // Subscribe user to newsletter.
    $subscriber = Subscriber::loadByMail($user->getEmail(), 'create', NULL, 'check_trust');
    foreach (Newsletter::loadMultiple() as $newsletter) {
      if ($newsletter->get('new_account') == 'on') {
        if ($newsletter->id() == 'default' && $user->get('field_interests')->value != 'buy') {
          continue;
        }
        $subscriber->subscribe($newsletter->id());
      }
      Subscriber::$userRegSubscriber = $subscriber;
      $subscriber->save();
    }
  }
}

/**
 * Implements hook_field_widget_single_element_WIDGET_TYPE_form_alter() for options_shs.
 */
function mezabirza_field_widget_single_element_options_shs_form_alter(array &$element, FormStateInterface $form_state, array $context) {
  $element['#maxlength'] = 255;
}

/**
 * Implements hook_page_attachments_alter().
 */
function mezabirza_page_attachments_alter(array &$attachments) {
  // Get the current route name.
  $route_name = \Drupal::routeMatch()->getRouteName();

  // Define meta descriptions for user pages.
  $meta_descriptions = [
    'user.login' => t('Log in to your account to access exclusive features and content.'),
    'user.register' => t('Register for a new account to join our community and enjoy personalized services.'),
    'user.pass' => t('Reset your password to regain access to your account.'),
  ];

  // Check if the current route matches a user page.
  if (array_key_exists($route_name, $meta_descriptions)) {
    // Add the meta description to the page.
    $attachments['#attached']['html_head'][] = [
      [
        '#tag' => 'meta',
        '#attributes' => [
          'name' => 'description',
          'content' => $meta_descriptions[$route_name],
        ],
      ],
      'meta_description',
    ];
  }
}
