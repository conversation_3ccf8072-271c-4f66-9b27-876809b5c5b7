<?php

namespace Drupal\userpoints\Plugin\Commerce\CheckoutPane;

use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Url;
use Drupal\commerce_checkout\Plugin\Commerce\CheckoutPane\CheckoutPaneBase;

/**
 * Provides the redirection pane.
 *
 * @CommerceCheckoutPane(
 *   id = "redirection",
 *   label = @Translation("Redirect"),
 * )
 */
class RedirectPane extends CheckoutPaneBase {

  /**
   * {@inheritdoc}
   */
  public function buildPaneForm(array $pane_form, FormStateInterface $form_state, array &$complete_form) {
    $pane_form['form'] = [
      'message' => [
        '#theme' => 'order_finalized',
        '#order' => $this->order,
        '#invoice_url' => $this->t('To view your orders, please click <a href=@url>here.</a>', ['@url' => Url::fromRoute('view.commerce_user_invoices.invoice_page', ['user' => $this->order->getCustomerId()])->toString()]),
      ],
    ];

    return $pane_form;
  }

}
