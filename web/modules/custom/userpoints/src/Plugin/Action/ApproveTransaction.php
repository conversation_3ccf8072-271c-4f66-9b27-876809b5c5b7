<?php

namespace Drupal\userpoints\Plugin\Action;

use <PERSON><PERSON>al\Core\Action\ActionBase;
use <PERSON>upal\Core\Session\AccountInterface;
use <PERSON><PERSON>al\userpoints\Entity\TransactionInterface;

/**
 * Provides an approve transaction action.
 *
 * @Action(
 *   id = "userpoints_approve_transaction",
 *   label = @Translation("Approve transaction"),
 *   type = "transaction",
 *   confirm = TRUE,
 *   category = @Translation("Transactions")
 * )
 */
class ApproveTransaction extends ActionBase {

  /**
   * {@inheritdoc}
   */
  public function access($auction, ?AccountInterface $account = NULL, $return_as_object = FALSE) {
    /** @var \Drupal\auctions\AuctionInterface $auction */
    $access = $auction->access('update', $account, TRUE);
    return $return_as_object ? $access : $access->isAllowed();
  }

  /**
   * {@inheritdoc}
   */
  public function execute($transaction = NULL) {
    /** @var \Drupal\userpoints\Entity\TransactionInterface $transaction */
    $transaction->set('status', TransactionInterface::APPROVED);
    $transaction->save();
  }

}
