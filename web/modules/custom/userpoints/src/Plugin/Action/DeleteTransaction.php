<?php

namespace Drupal\userpoints\Plugin\Action;

use <PERSON>upal\Core\Action\ActionBase;
use <PERSON>upal\Core\Session\AccountInterface;

/**
 * Provides a delete transaction action.
 *
 * @Action(
 *   id = "userpoints_delete_transaction",
 *   label = @Translation("Delete transaction"),
 *   type = "transaction",
 *   confirm = TRUE,
 *   category = @Translation("Transactions")
 * )
 */
class DeleteTransaction extends ActionBase {

  /**
   * {@inheritdoc}
   */
  public function access($transaction, ?AccountInterface $account = NULL, $return_as_object = FALSE) {
    $access = $transaction->access('delete', $account, TRUE);
    return $return_as_object ? $access : $access->isAllowed();
  }

  /**
   * {@inheritdoc}
   */
  public function execute($transaction = NULL) {
    /** @var \Drupal\userpoints\Entity\TransactionInterface $transaction */
    $transaction->delete();
  }

}
