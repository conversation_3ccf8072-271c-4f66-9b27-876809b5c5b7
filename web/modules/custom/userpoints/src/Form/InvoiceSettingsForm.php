<?php

namespace Drupal\userpoints\Form;

use Drupal\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;

/**
 * Provides invoice settings.
 *
 * @package Drupal\userpoints\Form
 */
class InvoiceSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'userpoints_invoice_settings';
  }

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['userpoints.invoice_settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('userpoints.invoice_settings');
    $form['vendor_name'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Vendor name'),
      '#default_value' => $config->get('vendor_name'),
    ];
    $form['vendor_address'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Vendor address'),
      '#default_value' => $config->get('vendor_address'),
    ];
    $form['vendor_registration_no'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Vendor registration number'),
      '#default_value' => $config->get('vendor_registration_no'),
    ];
    $form['vendor_vat_no'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Vendor VAT number'),
      '#default_value' => $config->get('vendor_vat_no'),
    ];
    $form['vendor_bank_name'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Vendor bank name'),
      '#default_value' => $config->get('vendor_bank_name'),
    ];
    $form['vendor_bank_code'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Vendor bank code'),
      '#default_value' => $config->get('vendor_bank_code'),
    ];
    $form['vendor_bank_account'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Vendor bank account'),
      '#default_value' => $config->get('vendor_bank_account'),
    ];
    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $config = $this->config('userpoints.invoice_settings');
    foreach ($form_state->getValues() as $key => $value) {
      if (strpos($key, 'vendor_') !== FALSE) {
        $config->set($key, $value);
      }
    }
    $config->save();
    parent::submitForm($form, $form_state);
  }

}
