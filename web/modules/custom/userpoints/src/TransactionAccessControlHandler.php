<?php

namespace Drupal\userpoints;

use <PERSON>upal\Core\Access\AccessResult;
use <PERSON>upal\Core\Entity\EntityAccessControlHandler;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;

/**
 * Access controller for the Transaction entity.
 *
 * @see \Drupal\userpoints\Entity\Transaction.
 */
class TransactionAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    /** @var \Drupal\userpoints\Entity\TransactionInterface $entity */
    switch ($operation) {
      case 'view':
        return AccessResult::allowedIfHasPermission($account, 'view transaction entities');

      case 'update':
        return AccessResult::allowedIfHasPermission($account, 'edit transaction entities');

      case 'delete':
        return AccessResult::allowedIfHasPermission($account, 'delete transaction entities');
    }

    return AccessResult::neutral();
  }

  /**
   * {@inheritdoc}
   */
  protected function checkCreateAccess(AccountInterface $account, array $context, $entity_bundle = NULL) {
    return AccessResult::allowedIfHasPermission($account, 'add transaction entities');
  }

}
