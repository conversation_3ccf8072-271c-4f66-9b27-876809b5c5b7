# Transaction routing definition
transaction.settings_tab:
  route_name: transaction.settings
  title: 'Settings'
  base_route: transaction.settings

entity.transaction.collection:
  route_name: view.transactions.overview
  base_route: entity.user.collection
  title: 'Transactions'
  weight: 10

entity.transaction.user:
  route_name: view.transactions.user
  base_route: entity.user.canonical
  title: 'Transactions'
  weight: 10

userpoints.point_purchase:
  route_name: userpoints.point_purchase
  base_route: entity.user.canonical
  title: 'Buy points'
  weight: 10

entity.transaction.canonical:
  route_name: entity.transaction.canonical
  base_route: entity.transaction.canonical
  title: 'View'

entity.transaction.edit_form:
  route_name: entity.transaction.edit_form
  base_route: entity.transaction.canonical
  title: 'Edit'

entity.transaction.delete_form:
  route_name:  entity.transaction.delete_form
  base_route:  entity.transaction.canonical
  title: Delete
  weight: 10

userpoints.invoice_settings:
  route_name: userpoints.invoice_settings
  title: 'Invoice settings'
  base_route: userpoints.invoice_settings

userpoints.buyer_registration_settings:
  route_name: userpoints.buyer_registration_settings
  title: 'Buyer registration settings'
  base_route: userpoints.buyer_registration_settings

userpoints.transaction.overview:
  route_name: view.user_transaction_sum.overview
  base_route: entity.user.collection
  title: 'User transaction sum'
  weight: 10
