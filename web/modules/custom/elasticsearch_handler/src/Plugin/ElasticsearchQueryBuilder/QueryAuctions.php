<?php

namespace Drupal\elasticsearch_handler\Plugin\ElasticsearchQueryBuilder;

use Drupal\Core\Site\Settings;
use <PERSON><PERSON>al\elasticsearch_handler\Plugin\views\filter\FilterListCuttingArea;
use Drupal\elasticsearch_handler\Plugin\views\filter\FilterListForestVolume;
use Drupal\inqube\Plugin\ElasticsearchQueryBuilder\BaseIndexRootQueryBuilder;
use Drupal\views\Plugin\views\display\DisplayPluginBase;
use Drupal\views\ViewExecutable;

/**
 * Provides query builder for auctions.
 *
 * @ElasticsearchQueryBuilder(
 *   id = "auctions",
 *   label = @Translation("Auctions"),
 *   description = @Translation("Builds Elasticsearch query for auctions view")
 * )
 *
 * @package Drupal\elasticsearch_handler\Plugin\ElasticsearchQueryBuilder
 */
class QueryAuctions extends BaseIndexRootQueryBuilder {

  /**
   * Default Index roots.
   *
   * @var array
   */
  public $baseRoots = ['stumpage'];

  /**
   * Keyword query fields.
   *
   * @var array
   */
  public $sortFields = [
    'relevance' => '_score',
    'score' => '_score',
    'title' => 'title',
    'created' => 'created',
    'updated' => 'updated',
    'inqueb_source' => 'label',
    'inqueb_source_1' => 'end_time',
    'label' => 'label',
  ];

  /**
   * Filter and respective field mapping for should queries.
   *
   * @var array
   */
  public $shouldFilters = [
    'filter_auction_status' => 'status',
    'filter_administrative_areas_term' => 'administrative_area',
    'filter_price_group' => 'price_group',
    'filter_auction_type' => 'type',
  ];

  /**
   * Filter and respective field mapping for must queries.
   *
   * @var array
   */
  public $mustFilters = [
    'filter_auction_species' => 'species',
  ];

  /**
   * Filter and respective field mapping for should queries.
   *
   * @var array
   */
  public $rangeFilters = [
    'filter_cutting_area' => [
      'field' => 'cutting_area',
      'ranges' => FilterListCuttingArea::class,
    ],
    'filter_forest_volume' => [
      'field' => 'forest_volume',
      'ranges' => FilterListForestVolume::class,
    ],
  ];

  /**
   * Keyword filters.
   *
   * @var array
   */
  public $keywordFilters = ['keyword'];

  /**
   * {@inheritdoc}
   */
  public function init(ViewExecutable $view, DisplayPluginBase $display, ?array &$options = NULL) {
    parent::init($view, $display, $options);
    $this->cleanDateFilters([
      'filter_from',
      'filter_to',
    ], $this->cleanFilterValues);
  }

  /**
   * {@inheritdoc}
   */
  public function getAlteredRoots(array $base_roots): array {
    $roots = [];
    foreach ($base_roots as $base_root) {
      foreach ($this->languageManager->getLanguages() as $language) {
        $roots[$base_root . '_index_' . $language->getId() . '_' . Settings::get('elasticsearch_helper.environment', 'default')] = $base_root;
      }
    }

    return $roots;
  }

  /**
   * {@inheritdoc}
   */
  public function alterRootQuery(array &$query, $base_root, $root) {
    if (!empty($this->cleanFilterValues['filter_from'])) {
      $query['bool']['filter'][]['range']['end_time'] = [
        'gte' => $this->cleanFilterValues['filter_from'] * 1000,
      ];
    }
    if (!empty($this->cleanFilterValues['filter_to'])) {
      $query['bool']['filter'][]['range']['end_time'] = [
        'lte' => ($this->cleanFilterValues['filter_to'] + 60 * 60 * 24) * 1000,
      ];
    }
    if (isset($this->getFilterValues()['filter_has_bids']) && $this->getFilterValues()['filter_has_bids'] === '0') {
      $query['bool']['must'][]['term'] = ['has_bids' => TRUE];
    }
    if (!(empty($this->cleanFilterValues['filter_auction_type']) || $this->cleanFilterValues['filter_auction_type'][0] === 'any')) {
      $query['bool']['must'][]['term'] = ['status' => 'active'];
    }
    $query['bool']['must'][]['term'] = ['currency' => 'EUR'];
  }

  /**
   * {@inheritdoc}
   */
  public function alterFullQuery(array &$full_query) {
    // Aggregations for faceted search.
    $this->addTermAggregation($full_query, [
      'administrative_area' => 'administrative_area',
      'price_group' => 'price_group',
      'uid' => 'uid',
      'status' => 'status',
      'type' => 'type',
    ]);
    $this->addSumAggregation($full_query, [
      'sum_forest_volume' => 'forest_volume',
      'sum_current_price' => 'current_price',
      'sum_start_price' => 'start_price',
      'bids' => 'bids',
    ]);
    $this->addRangeAggregation($full_query, [
      [
        'field' => 'cutting_area',
        'ranges' => FilterListCuttingArea::getRanges(),
      ],
      [
        'field' => 'forest_volume',
        'ranges' => FilterListForestVolume::getRanges(),
      ],
    ]);
  }

  /**
   * Adds term aggregation.
   *
   * @param array $full_query
   *   The query.
   * @param array $fields
   *   Fields to add.
   */
  protected function addTermAggregation(array &$full_query, array $fields) {
    foreach ($fields as $name => $field_name) {
      $full_query['body']['aggs'][$name]['terms'] = [
        'field' => $field_name,
        'size' => 1000,
      ];
    }
  }

  /**
   * Adds sum aggregation.
   *
   * @param array $full_query
   *   The query.
   * @param array $fields
   *   Fields to add.
   */
  protected function addSumAggregation(array &$full_query, array $fields) {
    $full_query['body']['aggs']['has_bids']['terms'] = [
      'field' => 'has_bids',
    ];
    foreach ($fields as $name => $field_name) {
      $full_query['body']['aggs']['has_bids']['aggs'][$name]['sum'] = [
        'field' => $field_name,
      ];
    }
  }

  /**
   * Adds range aggregation.
   *
   * @param array $full_query
   *   The query.
   * @param array $ranges
   *   Ranges to add.
   */
  protected function addRangeAggregation(array &$full_query, array $ranges) {
    foreach ($ranges as $range) {
      $full_query['body']['aggs'][$range['name'] ?? $range['field']]['range'] = [
        'field' => $range['field'],
        'keyed' => TRUE,
        'ranges' => $this->getRangeAggregateFromRangeOptions($range['ranges']),
      ];
    }
  }

  /**
   * Returns range aggregation.
   *
   * @param array $ranges
   *   Ranges.
   *
   * @return array
   *   Ranges aggregate.
   */
  protected function getRangeAggregateFromRangeOptions(array $ranges) {
    $ranges = array_values($ranges);
    foreach ($ranges as $key => $range) {
      $ranges[$key] = [
        'key' => $range['key'],
        'from' => $range['from'],
        'to' => $range['to'] ?? NULL,
      ];
    }
    return $ranges;
  }

}
