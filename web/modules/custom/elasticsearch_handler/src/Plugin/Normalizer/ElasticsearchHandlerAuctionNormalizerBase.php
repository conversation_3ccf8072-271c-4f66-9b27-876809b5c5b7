<?php

namespace Drupal\elasticsearch_handler\Plugin\Normalizer;

use Drupal\auctions\Entity\Auction;
use Drupal\serialization\Normalizer\ContentEntityNormalizer;

/**
 * Class ElasticsearchHandlerNodeNormalizerBase.
 *
 * @package Drupal\elasticsearch_handler\Plugin\Normalizer
 */
abstract class ElasticsearchHandlerAuctionNormalizerBase extends ContentEntityNormalizer {

  use ElasticsearchHandlerNormalizerTrait;

  /**
   * The interface or class that this Normalizer supports.
   *
   * @var array
   */
  protected $supportedInterfaceOrClass = [Auction::class];

  /**
   * {@inheritdoc}
   */
  public function normalize($auction, $format = NULL, array $context = []): float|array|\ArrayObject|bool|int|string|null {
    /** @var \Drupal\auctions\AuctionInterface $auction */
    return [
      'id' => $auction->id(),
      'uuid' => $auction->uuid(),
      'uid' => $auction->getOwnerId(),
      'entity' => $auction->getEntityTypeId(),
      'bundle' => $auction->bundle(),
      'status' => $auction->status->value,
      'created' => $auction->getCreatedTime(),
      'changed' => $auction->getChangedTime(),
      'end_time' => (int) $auction->end_time->date->getTimestamp(),
      'start_price' => $auction->start_price->value,
      'current_price' => $auction->getCurrentPrice(),
      'currency' => $auction->currency->value,
    ];
  }

}
