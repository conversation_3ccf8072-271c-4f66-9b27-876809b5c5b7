<?php

namespace Drupal\elasticsearch_handler\Plugin\Normalizer;

use Drupal\Core\Entity\ContentEntityInterface;
use Dr<PERSON>al\Core\Entity\EntityInterface;
use Drupal\Core\Field\FieldItemListInterface;
use Drupal\file\Entity\File;
use Dr<PERSON>al\node\Entity\Node;

/**
 * Provides helper methods to normalize content.
 *
 * @package Drupal\elasticsearch_handler\Plugin\Normalizer
 */
trait ElasticsearchHandlerNormalizerTrait {

  /**
   * Returns used terms id list.
   *
   * @param \Drupal\Core\Entity\ContentEntityInterface $content
   *   Content entity.
   *
   * @return array
   *   Used terms.
   */
  protected function getUsedTerms(ContentEntityInterface $content) {
    $used_terms = [];
    $definitions = $content->getFieldDefinitions();
    /** @var \Drupal\field\Entity\FieldConfig $definition */
    foreach ($definitions as $definition) {
      // Find entity reference fields that has taxonomy handler and not empty.
      if ($definition->getType() === 'entity_reference' &&
        strpos($definition->getSetting('handler'), 'taxonomy_term') !== FALSE &&
        $content->hasField($definition->getName()) &&
        !$content->get($definition->getName())->isEmpty()
      ) {
        $this->setTerms($used_terms, array_column($content->get($definition->getName())->getValue(), 'target_id'));
      }
    }
    return array_values($used_terms);
  }

  /**
   * Gets target ids.
   *
   * @param \Drupal\Core\Field\FieldItemListInterface $items
   *   FieldItem list.
   *
   * @return array
   *   Array of target ids.
   */
  protected function getTermIds(FieldItemListInterface $items) {
    return array_column($items->getValue(), 'target_id');
  }

  /**
   * Gets target ids of parent terms.
   *
   * @param \Drupal\Core\Field\FieldItemListInterface $items
   *   FieldItem list.
   * @param bool $include_child
   *   FieldItem list.
   *
   * @return array
   *   Array of target ids.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  protected function getTermParentIds(FieldItemListInterface $items, $include_child = TRUE) {
    if ($items->isEmpty()) {
      return [];
    }
    $terms = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadAllParents($items->target_id);

    if (!$include_child) {
      unset($terms[$items->target_id]);
    }

    $data = [];
    /** @var \Drupal\taxonomy\Entity\Term $term */
    foreach ($terms as $term) {
      $data[] = $term->id();
    }

    return $data;
  }

  /**
   * Gets values.
   *
   * @param \Drupal\Core\Field\FieldItemListInterface $items
   *   FieldItem list.
   *
   * @return array
   *   Array of values.
   */
  protected function getValues(FieldItemListInterface $items) {
    if ($items->isEmpty()) {
      return NULL;
    }
    return array_column($items->getValue(), 'value');
  }

  /**
   * Returns referenced ids.
   *
   * @param \Drupal\Core\Field\FieldItemListInterface $itemList
   *   Field item list.
   *
   * @return array|null
   *   Target ids.
   */
  protected function getTargetIds(FieldItemListInterface $itemList): ?array {
    if ($itemList->isEmpty()) {
      return NULL;
    }
    return array_column($itemList->getValue(), 'target_id');
  }

  /**
   * Sets given terms in list using value as key.
   *
   * @param array $term_list
   *   Term list.
   * @param array $terms
   *   New terms.
   */
  protected function setTerms(array &$term_list, array $terms) {
    foreach ($terms as $tid) {
      $term_list[$tid] = $tid;
    }
  }

  /**
   * Creates styled image and returns it's url.
   *
   * If null passed as $image returns empty string.
   *
   * @param \Drupal\file\Entity\File|null $image
   *   Image FileEntity.
   * @param string $image_style
   *   Machine name of image style.
   *
   * @return string
   *   Image url.
   */
  protected function getStyledImageUrl(?File $image, string $image_style): string {
    return !$image || $image->getEntityTypeId() !== 'file'
      ? ''
      : $image->getFileUri();
  }

  /**
   * Returns labels of node field's referenced entities.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Node.
   * @param string $field_name
   *   Machine name of a node field.
   * @param bool $translate
   *   Should the label be translated into Node's language.
   *
   * @return array
   *   Array of entity labels.
   */
  protected function getReferencedEntitiesLabels(Node $node, string $field_name, bool $translate = FALSE): array {
    if (!$node->hasField($field_name)) {
      return [];
    }
    $language_id = $translate ? $node->language()->getId() : NULL;

    // phpcs:ignore
    return array_map(function (EntityInterface $entity) use ($language_id) {
      if ($language_id && $entity->hasTranslation($language_id)) {
        return $entity->getTranslation($language_id)->label();
      }
      return $entity->label();
    }, $node->get($field_name)->referencedEntities());
  }

  /**
   * Returns translated node bundle's label.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Node.
   *
   * @return string
   *   String.
   */
  protected function getNodeBundleLabel(Node $node): string {
    $language_manager = \Drupal::languageManager();
    $original_language = $language_manager->getConfigOverrideLanguage();
    $language_manager->setConfigOverrideLanguage($node->language());
    $label = \Drupal::entityTypeManager()->getStorage('node_type')->load($node->bundle())->label();
    $language_manager->setConfigOverrideLanguage($original_language);

    return $label;
  }

  /**
   * Returns search content.
   *
   * @param \Drupal\Core\Entity\ContentEntityInterface $entity
   *   Entity.
   *
   * @return string
   *   Content.
   */
  protected function getSearchContent(ContentEntityInterface $entity): string {
    $values = '';
    foreach ($entity->getFieldDefinitions() as $field) {
      if (str_contains($field->getType(), 'entity_reference_revisions') && !$entity->get($field->getName())->isEmpty()) {
        foreach ($entity->get($field->getName())->referencedEntities() as $item) {
          $values .= $this->getSearchContent($item);
        }
      }
      elseif (str_contains($field->getType(), 'text') && !$entity->get($field->getName())->isEmpty()) {
        $values .= ' ' . implode(' ', array_column($entity->get($field->getName())->getValue(), 'value'));
      }
    }

    return str_replace(['&nbsp;', '  '], ' ', strip_tags(str_replace('><', '> <', htmlspecialchars_decode($values))));
  }

  /**
   * Returns first entity referenced by field name listed in $fields.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Node.
   * @param array $fields
   *   Array of node field names.
   *
   * @return \Drupal\Core\Entity\EntityInterface|null
   *   Entity.
   */
  protected function getFieldEntity(Node $node, array $fields): ?EntityInterface {
    foreach ($fields as $field) {
      if ($node->hasField($field) && !$node->get($field)->isEmpty()) {
        return $node->get($field)->entity;
      }
    }
    return NULL;
  }

}
