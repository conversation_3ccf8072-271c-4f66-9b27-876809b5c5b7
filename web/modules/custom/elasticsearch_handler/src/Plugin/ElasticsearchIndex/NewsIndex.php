<?php

namespace Drupal\elasticsearch_handler\Plugin\ElasticsearchIndex;

use <PERSON><PERSON><PERSON>\elasticsearch_helper\Elasticsearch\Index\FieldDefinition;
use Dr<PERSON>al\elasticsearch_helper\Elasticsearch\Index\MappingDefinition;

/**
 * Class ArticleIndex provides index mapping for Article nodes.
 *
 * @ElasticsearchIndex(
 *   id = "news_index",
 *   label = @Translation("news index"),
 *   bundle = "news",
 *   entityType = "node",
 *   normalizerFormat = "news",
 *   deriver = "Drupal\elasticsearch_handler\Plugin\Derivative\NewsIndexDeriver"
 * )
 *
 * @package Drupal\elasticsearch_handler\Plugin\ElasticsearchIndex
 */
class NewsIndex extends MultilingualContentIndex {

  /**
   * {@inheritdoc}
   */
  public function getMappingDefinition(array $context = []) {

    return MappingDefinition::create()
      ->addProperty('langcode', FieldDefinition::create('keyword'))
      ->addProperty('id', FieldDefinition::create('integer'))
      ->addProperty('uuid', FieldDefinition::create('keyword'))
      ->addProperty('title', FieldDefinition::create('text'))
      ->addProperty('title_keyword', FieldDefinition::create('keyword'))
      ->addProperty('status', FieldDefinition::create('keyword'))
      ->addProperty('entity', FieldDefinition::create('keyword'))
      ->addProperty('bundle', FieldDefinition::create('keyword'))
      ->addProperty('entity_label', FieldDefinition::create('keyword'))
      ->addProperty('bundle_label', FieldDefinition::create('keyword'))
      ->addProperty('url_internal', FieldDefinition::create('keyword'))
      ->addProperty('url_alias', FieldDefinition::create('keyword'))
      ->addProperty('label', FieldDefinition::create('keyword'))
      ->addProperty('used_terms', FieldDefinition::create('keyword'))
      ->addProperty('created', FieldDefinition::create('date')->addOption('format', 'epoch_second'))
      ->addProperty('updated', FieldDefinition::create('date')->addOption('format', 'epoch_second'))
      ->addProperty('content', FieldDefinition::create('text'))
      ->addProperty('search_content', FieldDefinition::create('text'))
      ->addProperty('thumbnail', FieldDefinition::create('keyword'));

  }

}
