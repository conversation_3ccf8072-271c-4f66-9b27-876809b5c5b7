<?php

namespace Drupal\elasticsearch_handler\Plugin\ElasticsearchIndex;

use Dr<PERSON>al\Core\Language\LanguageManagerInterface;
use Drupal\Core\Site\Settings;
use Dr<PERSON>al\elasticsearch_handler\ElasticsearchIndexSettings;
use Dr<PERSON><PERSON>\elasticsearch_helper\Elasticsearch\Index\FieldDefinition;
use Dr<PERSON>al\elasticsearch_helper\Elasticsearch\Index\MappingDefinition;
use Drupal\elasticsearch_helper\Plugin\ElasticsearchIndexBase;
use Elastic\Elasticsearch\Client;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Serializer\Serializer;

/**
 * Provides index for multilingual content.
 *
 * @package Drupal\elasticsearch_handler\Plugin\ElasticsearchIndex
 */
abstract class MultilingualContentIndex extends ElasticsearchIndexBase {

  /**
   * Language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  protected $languageManager;

  /**
   * {@inheritdoc}
   */
  public function __construct(array $configuration, $plugin_id, $plugin_definition, Client $client, Serializer $serializer, LoggerInterface $logger, LanguageManagerInterface $languageManager) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $client, $serializer, $logger);
    $this->languageManager = $languageManager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('elasticsearch_helper.elasticsearch_client'),
      $container->get('serializer'),
      $container->get('logger.factory')->get('elasticsearch_helper'),
      $container->get('language_manager')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function serialize($source, $context = []) {
    /** @var \Drupal\core\Entity\ContentEntityBase $source */
    $data = parent::serialize($source, $context);
    // Add the language code to be used as a token.
    $data['langcode'] = $source->language()->getId();

    return $data;
  }

  /**
   * {@inheritdoc}
   */
  public function index($source) {
    /** @var \Drupal\core\Entity\ContentEntityBase $source */
    foreach (array_keys($source->getTranslationLanguages()) as $langcode) {
      if ($source->hasTranslation($langcode)) {
        parent::index($source->getTranslation($langcode));
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public function delete($source) {
    /** @var \Drupal\core\Entity\ContentEntityBase $source */
    foreach (array_keys($source->getTranslationLanguages()) as $langcode) {
      if ($source->hasTranslation($langcode)) {
        parent::delete($source->getTranslation($langcode));
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public function setup() {
    // Create one index per language, so that we can have different analyzers.
    foreach ($this->languageManager->getLanguages() as $langcode => $language) {
      $context = [
        'langcode' => $langcode,
        'language' => $language,
        'environment' => Settings::get('elasticsearch_helper.environment', 'default'),
        'analyzer' => 'custom_' . $langcode,
      ];
      // Get index definition.
      $index_definition = $this->getIndexDefinition($context);
      // Get index name.
      $index_name = $this->getIndexName($context);

      if (!$this->client->indices()->exists(['index' => $index_name])->asBool()) {
        $this->client->indices()->create([
          'index' => $index_name,
          'body' => array_merge($index_definition->toArray(), static::getSettings($langcode)),
        ]);
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getIndexName(array $data = []) {
    $data['environment'] = Settings::get('elasticsearch_helper.environment', 'default');
    return $this->replacePlaceholders($this->pluginDefinition['indexName'], $data);
  }

  /**
   * {@inheritdoc}
   */
  public function getMappingDefinition(array $context = []) {
    return MappingDefinition::create()
      ->addProperty('id', FieldDefinition::create('integer'))
      ->addProperty('uuid', FieldDefinition::create('keyword'))
      ->addProperty('uid', FieldDefinition::create('keyword'))
      ->addProperty('entity', FieldDefinition::create('keyword'))
      ->addProperty('bundle', FieldDefinition::create('keyword'))
      ->addProperty('status', FieldDefinition::create('keyword'))
      ->addProperty('created', FieldDefinition::create('date')->addOption('format', 'epoch_second'))
      ->addProperty('updated', FieldDefinition::create('date')->addOption('format', 'epoch_second'))
      ->addProperty('end_time', FieldDefinition::create('date')->addOption('format', 'epoch_second'))
      ->addProperty('start_price', FieldDefinition::create('float'))
      ->addProperty('current_price', FieldDefinition::create('float'))
      ->addProperty('currency', FieldDefinition::create('keyword'));
  }

  /**
   * Get the index setting using language code.
   *
   * @param string $langcode
   *   The language code of filter.
   *
   * @return array
   *   Settings array.
   */
  public static function getSettings($langcode) {
    return [
      'settings' => [
        'max_ngram_diff' => 20,
        'analysis' => [
          'filter' => ElasticsearchIndexSettings::getAllTokenFilters(),
          'analyzer' => ElasticsearchIndexSettings::getAnalyzers([
            'custom_search_analyzer',
            'custom_regulation_analyzer',
            'custom_' . $langcode,
          ]),
          'tokenizer' => ElasticsearchIndexSettings::getTokenizers(
            ['trigram_tokenizer', 'edge_tokenizer']
          ),
        ],
      ],
    ];
  }

}
