.star-rating-wrapper {
  display: inline-block;
}

.star-rating-wrapper .star {
  font-size: 2em;
  color: #ccc;
  cursor: pointer;
  display: inline-block;
}

.star-rating-wrapper.view-only .star {
  cursor: default;
}

.star-rating-wrapper .star.selected,
.star-rating-wrapper:not(.view-only) .star.hover {
  color: #f2b600;
}

.star-rating-wrapper:not(.view-only) .star:hover ~ .star {
  color: #ccc;
}

.rating-button-loading {
  background: grey !important;
  cursor: not-allowed !important;
}

.comment-area {
  width: 100%;
}

.rating-status-area > div {
  padding: 1rem;
  text-align: center;
}
