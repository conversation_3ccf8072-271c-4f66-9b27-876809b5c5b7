<?php

/**
 * @file
 * Contains mb_rating.module.
 */

use Drupal\Core\Entity\Display\EntityViewDisplayInterface;
use Drupal\Core\Url;
use Drupal\auctions\Entity\Auction;
use Drupal\auctions\Entity\AuctionType;

/**
 * Implements hook_entity_extra_field_info().
 */
function mb_rating_entity_extra_field_info() {
  $extra = [];
  foreach (AuctionType::loadMultiple() as $auction_type) {
    $extra['auction'][$auction_type->id()]['display']['rate'] = [
      'label' => t('Rate winner'),
      'description' => t('Rating field for the auction winner.'),
      'weight' => 100,
      'visible' => TRUE,
    ];
  }
  return $extra;
}

/**
 * Implements hook_ENTITY_TYPE_view() for auction.
 */
function mb_rating_auction_view(array &$build, Auction $entity, EntityViewDisplayInterface $display) {
  // Attach the rate field.
  if ($display->getComponent('rate') && $entity->isFinished() && (\Drupal::currentUser()->id() == $entity->getOwnerId() || \Drupal::currentUser()->hasPermission('access auction overview')) && $entity->hasBids()) {
    $rating = \Drupal::entityTypeManager()->getStorage('rating')->loadByProperties([
      'auction' => $entity->id(),
      'uid' => $entity->getOwnerId(),
      'user' => $entity->get('winner')->target_id,
    ]);
    $build['rate'] = [
      '#type' => 'container',
      'label' => [
        '#markup' => '<h2 class="section-title">' . t('Rate collaboration') . '</h2>',
      ],
      '#attributes' => [
        'class' => [
          'field--name-field-rating',
        ],
      ],
      'value' => [
        '#type' => 'container',
        '#attributes' => [
          'class' => [
            'field--rate',
          ],
        ],
        'rating' => [
          '#type' => 'hidden',
          '#value' => $rating ? reset($rating)->get('rating')->value : 0,
          '#attributes' => [
            'class' => [
              'star-rating-input',
              $entity->getOwnerId() == \Drupal::currentUser()->id() || \Drupal::currentUser()->hasPermission('access rating overview') ? '' : 'view-only',
            ],
            'data-auction' => $entity->id(),
          ],
          '#attached' => [
            'library' => [
              'mb_rating/star_rating',
            ],
            'drupalSettings' => [
              'star_rating' => [
                'max_stars' => 5,
                'ajax_save_rating' => Url::fromRoute('mb_rating.ajax_save_rating')->toString(),
                'ajax_save_comment' => Url::fromRoute('mb_rating.ajax_save_comment')->toString(),
              ],
            ],
          ],
        ],
        'comment_button' => [
          '#type' => 'submit',
          '#value' => t('Notes'),
          '#attributes' => [
            'class' => [
              'add-comment',
              'button--action',
              'button--comment',
            ],
            'data-twig-suggestion' => 'button_comment',
          ],
        ],
        'comment_area' => [
          '#type' => 'container',
          '#attributes' => [
            'class' => [
              'comment-area',
            ],
            'style' => 'display: none;',
          ],
          'status_area' => [
            '#type' => 'html_tag',
            '#tag' => 'div',
            '#attributes' => [
              'class' => [
                'rating-status-area',
                'messages__wrapper',
              ],
            ],
          ],
          'comment' => [
            '#type' => 'textarea',
            '#title' => t('Comment'),
            '#value' => $rating ? reset($rating)->get('comment')->value : '',
            '#attributes' => [
              'class' => [
                'comment-textarea',
              ],
              'data-auction' => $entity->id(),
            ],
            '#suffix' => '<em>' . t('Your comments will only be visible to you and site administrators.') . '</em>',
          ],
          'submit' => [
            '#type' => 'submit',
            '#value' => t('Save'),
            '#attributes' => [
              'class' => [
                'comment-submit',
                'button--primary',
              ],
            ],
          ],
        ],
      ],
    ];
  }
}
