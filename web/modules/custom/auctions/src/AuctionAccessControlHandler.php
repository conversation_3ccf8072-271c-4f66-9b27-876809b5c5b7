<?php

namespace Drupal\auctions;

use Drupal\Core\Access\AccessResult;
use Drupal\Core\Entity\EntityAccessControlHandler;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;

/**
 * Defines the access control handler for the auction entity type.
 */
class AuctionAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    /** @var \Drupal\auctions\AuctionInterface $entity */
    switch ($operation) {
      case 'view':
        if (in_array(\Drupal::routeMatch()->getRouteName(), ['leaflet_views.ajax_popup'])) {
          return AccessResult::allowed();
        }
        $secretLinkAccess = ($entity->isActive() && $acct = \Drupal::request()->get('acct')) && $entity->getAccessToken() == $acct;
        $userHasPrivileges = $account->hasPermission('change auction state') || $entity->getOwnerId() == $account->id();
        $hasViewState = $account->hasPermission('view auction') && ($entity->isActive() || $entity->isFinished() || $entity->isCanceled());
        if ($hasViewState && $entity->field_private->value && !$userHasPrivileges) {
          if (!in_array($account->id(), array_column($entity->field_participants->getValue(), 'target_id'))) {
            return AccessResult::forbidden('private_auction_access_denied');
          }
        }
        if ($secretLinkAccess || $userHasPrivileges || $hasViewState || $entity->getOwnerId() == $account->id()) {
          return AccessResult::allowed();
        }
        return AccessResult::forbidden('auction_access_denied');

      case 'view teaser':
        return AccessResult::allowedIf($entity->isActive() || $entity->isFinished() || $entity->isCanceled());

      case 'update':
        $userHasPrivileges = $account->hasPermission('change auction state');
        $hasEditableState = $entity->canBeSubmitted() && $entity->getOwnerId() == $account->id();
        return AccessResult::allowedIf($hasEditableState || $userHasPrivileges);

      case 'delete':
        $userHasPrivileges = $account->hasPermission('delete auction') || $account->hasPermission('administer auction');
        return AccessResult::allowedIf($userHasPrivileges && $entity->isInactive());

      default:
        // No opinion.
        return AccessResult::neutral();
    }

  }

  /**
   * {@inheritdoc}
   */
  protected function checkCreateAccess(AccountInterface $account, array $context, $entity_bundle = NULL) {
    return AccessResult::allowedIfHasPermissions($account, [
      'create auction',
      'administer auction',
    ], 'OR');
  }

}
