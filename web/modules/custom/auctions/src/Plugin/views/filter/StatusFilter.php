<?php

namespace Drupal\auctions\Plugin\views\filter;

use Drupal\views\FieldAPIHandlerTrait;
use Drupal\views\Plugin\views\display\DisplayPluginBase;
use Drupal\views\Plugin\views\filter\ManyToOne;
use Dr<PERSON>al\views\ViewExecutable;

/**
 * Provides Status field handler.
 *
 * @ViewsFilter("auctions_status")
 */
class StatusFilter extends ManyToOne {
  use FieldAPIHandlerTrait;

  /**
   * {@inheritdoc}
   */
  public function init(ViewExecutable $view, DisplayPluginBase $display, ?array &$options = NULL) {
    parent::init($view, $display, $options);
    $this->valueOptions = auctions_auction_status_allowed_values_callback();
  }

}
