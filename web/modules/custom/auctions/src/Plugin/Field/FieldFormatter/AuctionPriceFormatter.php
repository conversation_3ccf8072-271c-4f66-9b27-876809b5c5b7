<?php

namespace Drupal\auctions\Plugin\Field\FieldFormatter;

use Drupal\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\FormatterBase;

/**
 * Plugin implementation of the 'auction_amount' formatter.
 *
 * @FieldFormatter(
 *   id = "auction_amount",
 *   label = @Translation("Auction amount"),
 *   field_types = {
 *     "integer",
 *     "float"
 *   }
 * )
 */
class AuctionPriceFormatter extends FormatterBase {

  /**
   * {@inheritdoc}
   */
  public function settingsSummary() {
    $summary[] = t('Display formatted auction amount');
    return $summary;
  }

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    $elements = [];
    /** @var \Drupal\auctions\Entity\Auction $auction */
    $auction = $items->getEntity();
    foreach ($items as $delta => $item) {
      $output = $item->value == 0 ? '-' : $auction->formatPrice($item->value);
      $elements[$delta] = ['#markup' => $output];
    }

    return $elements;
  }

}
