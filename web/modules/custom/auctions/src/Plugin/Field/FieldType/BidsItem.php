<?php

namespace Dr<PERSON>al\auctions\Plugin\Field\FieldType;

use <PERSON>upal\Core\Field\FieldItemBase;
use <PERSON><PERSON>al\Core\Field\FieldStorageDefinitionInterface;
use <PERSON>upal\Core\TypedData\DataDefinition;

/**
 * Defines the 'auctions_bids' field type.
 *
 * @FieldType(
 *   id = "auctions_bids",
 *   label = @Translation("Bids"),
 *   description = @Translation("Bids"),
 *   category = @Translation("General"),
 *   default_widget = "string",
 *   default_formatter = "auction_bid"
 * )
 */
class BidsItem extends FieldItemBase {

  /**
   * {@inheritdoc}
   */
  public function isEmpty() {
    return FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public static function propertyDefinitions(FieldStorageDefinitionInterface $field_definition) {
    $properties['value'] = DataDefinition::create('string')
      ->setLabel(t('Bids'))
      ->setComputed(TRUE);

    return $properties;
  }

  /**
   * {@inheritdoc}
   */
  public static function schema(FieldStorageDefinitionInterface $field_definition) {
    return [];
  }

}
