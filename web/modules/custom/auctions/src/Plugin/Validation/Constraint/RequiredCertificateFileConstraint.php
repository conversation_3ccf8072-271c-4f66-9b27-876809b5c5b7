<?php

declare(strict_types=1);

namespace Drupal\auctions\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;

/**
 * Provides a AuctionsRequiredCertificateFile constraint.
 *
 * @Constraint(
 *   id = "AuctionsRequiredCertificateFile",
 *   label = @Translation("RequiredCertificateFile", context = "Validation"),
 * )
 */
final class RequiredCertificateFileConstraint extends Constraint {

  /**
   * The message that will be shown if the constraint is violated.
   *
   * @var string
   */
  public string $message = 'Felling permit file is required.';

}
