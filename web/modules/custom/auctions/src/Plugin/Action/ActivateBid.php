<?php

namespace Dr<PERSON>al\auctions\Plugin\Action;

use Drupal\Core\Access\AccessResult;
use Drupal\Core\Action\ActionBase;
use <PERSON>upal\Core\Session\AccountInterface;

/**
 * Provides activate bid action.
 *
 * @Action(
 *   id = "activate_bid",
 *   label = @Translation("Activate bid"),
 *   type = "bid",
 *   confirm = TRUE,
 *   category = @Translation("Bids")
 * )
 */
class ActivateBid extends ActionBase {

  /**
   * {@inheritdoc}
   */
  public function access($transaction, ?AccountInterface $account = NULL, $return_as_object = FALSE) {
    if ($account->hasPermission('access bid overview')) {
      return $return_as_object ? AccessResult::allowed() : TRUE;
    }
    return $return_as_object ? AccessResult::forbidden() : FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function execute($bid = NULL) {
    /** @var \Drupal\auctions\BidInterface $bid */
    $bid->setStatus(TRUE)->save();
  }

}
