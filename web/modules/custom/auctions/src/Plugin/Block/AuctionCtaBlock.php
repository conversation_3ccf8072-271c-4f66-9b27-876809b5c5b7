<?php

namespace Dr<PERSON>al\auctions\Plugin\Block;

use <PERSON><PERSON><PERSON>\Component\Render\FormattableMarkup;
use <PERSON><PERSON>al\Core\Access\AccessResult;
use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON><PERSON>\Core\Session\AccountInterface;
use Dr<PERSON>al\Core\Url;
use <PERSON><PERSON><PERSON>\user\Entity\User;
use <PERSON><PERSON><PERSON>\userpoints\TransactionHandler;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides an auction cta block.
 *
 * @Block(
 *   id = "auctions_auction_cta",
 *   admin_label = @Translation("Auction CTA"),
 *   category = @Translation("Custom")
 * )
 */
class AuctionCtaBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * The transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  protected $transactionHandler;

  /**
   * Constructs a new UserpointsCtaBlock instance.
   *
   * @param array $configuration
   *   The plugin configuration, i.e. an array with configuration values keyed
   *   by configuration option name. The special key 'context' may be used to
   *   initialize the defined contexts by setting it to an array of context
   *   values keyed by context names.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    AccountInterface $current_user,
    TransactionHandler $transaction_handler,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->currentUser = $current_user;
    $this->transactionHandler = $transaction_handler;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('current_user'),
      $container->get('userpoints.transaction_handler')
    );
  }

  /**
   * {@inheritdoc}
   */
  protected function blockAccess(AccountInterface $account) {
    return AccessResult::allowedIf($account->isAuthenticated());
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build['my_auctions'] = [
      '#theme' => 'link__cta',
      '#title' => $this->t('My auctions'),
      '#icon' => 'auction',
      '#url' => Url::fromRoute('view.user_auctions.page', [
        'user' => \Drupal::currentUser()->id(),
      ]),
      '#attributes' => [
        'class' => [
          'secondary',
          'icon-mobile',
        ],
      ],
      '#cache' => [
        'contexts' => [
          'user',
        ],
      ],
    ];
    $build['create_auction'] = [
      '#theme' => 'link__cta',
      '#icon' => 'add',
      '#title' => $this->t('Create auction'),
      '#url' => Url::fromRoute('entity.auction.add_page'),
      '#attributes' => [
        'class' => [
          'secondary',
          'icon-mobile',
        ],
      ],
    ];
    $user = User::load(\Drupal::currentUser()->id()) ?? \Drupal::currentUser();
    $username = strlen($user->getDisplayName()) > 13
      ? new FormattableMarkup(mb_substr($user->getDisplayName(), 0, 12,) . '<span class="mobile-show">..</span>' . '<span class="mobile-hide">' . mb_substr($user->getDisplayName(), 12) . '</span>', [])
      : $user->getDisplayName();
    $build['account'] = [
      '#theme' => 'link__cta',
      '#title' => $username,
      '#icon' => 'account',
      '#url' => Url::fromRoute('entity.user.canonical', [
        'user' => $user->id(),
      ]),
      '#attributes' => [
        'class' => [
          'secondary',
          'icon-mobile',
          'righty',
        ],
      ],
      '#cache' => [
        'contexts' => [
          'user',
        ],
      ],
    ];
    if (array_intersect(['privileged', 'privileged_vip'], $user->getRoles())) {
      $build['account']['#icon'] = 'king';
    }
    return $build;
  }

}
