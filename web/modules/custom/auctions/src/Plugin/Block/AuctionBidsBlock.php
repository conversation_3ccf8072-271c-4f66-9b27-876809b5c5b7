<?php

namespace Dr<PERSON>al\auctions\Plugin\Block;

use <PERSON><PERSON><PERSON>\Component\Datetime\DateTimePlus;
use <PERSON><PERSON><PERSON>\Component\Utility\Crypt;
use <PERSON><PERSON>al\Core\Access\AccessResult;
use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Cache\Cache;
use <PERSON><PERSON>al\Core\Database\Connection;
use Dr<PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\auctions\AuctionInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides an auction bids block.
 *
 * @Block(
 *   id = "auctions_auction_bids",
 *   admin_label = @Translation("Auction bids"),
 *   category = @Translation("Custom")
 * )
 */
class AuctionBidsBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  protected $connection;

  /**
   * Constructs a new AuctionBidsBlock instance.
   *
   * @param array $configuration
   *   The plugin configuration, i.e. an array with configuration values keyed
   *   by configuration option name. The special key 'context' may be used to
   *   initialize the defined contexts by setting it to an array of context
   *   values keyed by context names.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   */
  public function __construct(array $configuration, $plugin_id, $plugin_definition, EntityTypeManagerInterface $entity_type_manager, Connection $connection) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeManager = $entity_type_manager;
    $this->connection = $connection;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('database')
    );
  }

  /**
   * {@inheritdoc}
   */
  protected function blockAccess(AccountInterface $account) {
    return $account->isAuthenticated() ? AccessResult::allowed() : AccessResult::forbidden();
  }

  /**
   * {@inheritdoc}
   */
  public function build($auction_id = NULL) {
    if (!$auction_id) {
      $auction = \Drupal::routeMatch()->getParameter('auction');
      if ($auction instanceof AuctionInterface && \Drupal::routeMatch()->getRouteName() == 'entity.auction.canonical') {
        $auction_id = $auction->id();
      }
    }
    if (!$auction_id) {
      return [];
    }
    $bids = $this->connection
      ->select('bid', 'b')
      ->fields('b', ['uid', 'amount', 'created', 'id', 'currency', 'auto'])
      ->orderBy('id')
      ->condition('status', TRUE)
      ->condition('auction', $auction_id)
      ->execute();
    if (empty($bids)) {
      return [];
    }
    $rows = [];
    $build['title'] = [
      '#type' => 'html_tag',
      '#tag' => 'h2',
      '#value' => $this->t('Bid history'),
      '#attributes' => [
        'class' => [
          'section-title',
        ],
      ],
    ];
    $build['toggle'] = [
      // Wrapper for the toggle button.
      '#type' => 'container',
      '#attributes' => [
        'class' => [
          'toggle',
        ],
      ],
      'button' => [
        '#type' => 'html_tag',
        '#tag' => 'button',
        '#value' => $this->t('Show my bids'),
        '#attributes' => [
          'class' => [
            'button--secondary--small',
            'toggle-my-bids',
          ],
        ],
      ],
    ];
    while ($bid = $bids->fetch()) {
      $time = DateTimePlus::createFromTimestamp($bid->created);
      $hash = Crypt::hmacBase64($bid->uid, 'mesparmezu');
      $rows[$bid->id] = [
        'data-bid' => $bid->id,
        'data-auto' => $bid->auto ? 1 : 0,
        'data-hash' => $hash,
        'data' => [
          [
            'data' => [
              '#markup' => $this->t('Bidder #@counter', ['@counter' => $this->bidderCounter($bid)]),
            ],
            'class' => ['user-data'],
            'data-user' => $hash,
          ],
          number_format($bid->amount, 2, '.', '') . ' ' . $bid->currency,
          [
            'data' => [
              '#markup' => $time->format('H:i:s') . '<br><i>' . $time->format('d-m-Y') . '</i>',
            ],
          ],
        ],
      ];
      if ($bid->auto) {
        $rows[$bid->id]['class'][] = 'auto';
      }
    }
    $build['content'] = [
      '#type' => 'table',
      '#header' => [
        'user' => [
          'data' => $this->t('User'),
        ],
        'amount' => [
          'data' => $this->t('Amount'),
        ],
        'time' => [
          'data' => $this->t('Time'),
        ],
      ],
      '#rows' => array_reverse($rows),
      '#empty' => $this->t('No bids'),
      '#attributes' => [
        'class' => [
          'bid__list',
        ],
      ],
    ];
    $build['legend'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $this->t('Autobid'),
      '#attributes' => [
        'class' => [
          'legend',
          'auto',
          'hidden',
        ],
      ],
    ];

    return $build;
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheTags() {
    return Cache::mergeTags(parent::getCacheTags(), [
      'bid_list',
      'user:' . \Drupal::currentUser()->id(),
      'user_list',
    ]);
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheContexts() {
    return Cache::mergeContexts(parent::getCacheContexts(), ['user', 'route']);
  }

  /**
   * Obfuscate user names for bid list.
   *
   * @param object $bid
   *   Bid.
   *
   * @return int
   *   Bidder counter id.
   */
  private function bidderCounter($bid) {
    $obfusacted_uids = &drupal_static(__FUNCTION__, []);
    // Obfuscate usernames and give each bidder a unique obfuscated number.
    if (!isset($obfusacted_uids[$bid->uid])) {
      $obfusacted_uids[$bid->uid] = count($obfusacted_uids) + 1;
    }

    return $obfusacted_uids[$bid->uid];
  }

}
