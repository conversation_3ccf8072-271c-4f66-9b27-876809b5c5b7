<?php

namespace Drupal\auctions\Plugin\QueueWorker;

use Drupal\Core\Link;
use Drupal\Core\Queue\QueueWorkerBase;

/**
 * Defines 'auctions_bid_insert' queue worker.
 *
 * @QueueWorker(
 *   id = "auctions_bid_insert",
 *   title = @Translation("Bid Insert"),
 *   cron = {"time" = 60}
 * )
 */
class AuctionBidInsertedWorker extends QueueWorkerBase {

  /**
   * {@inheritdoc}
   */
  public function processItem($data) {
    $bid = $data->bid;
    $auction = $bid->getAuction();
    \Drupal::service('auctions.language_switcher')->changeActiveLanguage($auction->language()->getId());
    $url = $auction->toUrl()->setAbsolute();
    $mailer = \Drupal::service('plugin.manager.mail');
    // Send owner mail for new bid.
    if (in_array('new_bet', array_column($auction->getOwner()->field_auction_notifications->getValue(), 'value'))) {
      $language = ['langcode' => $auction->getOwner()->getPreferredLangcode()];
      $params['subject'] = t('New bid in your auction.', [], $language);
      $params['body'][] = t('Hi, @user!', [
        '@user' => $auction->getOwner()->getDisplayName(),
      ], $language);
      $params['body'][] = t('Your auction "@auction" has new bid.', [
        '@auction' => $auction->label(),
      ], $language);
      $params['body'][] = t('Current highest bid: @amount', [
        '@amount' => $auction->formatPrice($bid->getAmount(), ''),
      ], $language);
      $params['body'][] = t('Link to auction: @link', [
        '@link' => Link::fromTextAndUrl($url->toString(), $url)->toString(),
      ], $language);
      $mailer->mail('auctions', 'auction_owner_bid', $auction->getOwner()->getEmail(), $auction->getOwner()->getPreferredLangcode(), $params);
    }
    // Send bid owner a message.
    $notification_type = $bid->autobid ? 'auto_bid_info' : 'bid_info';
    if (in_array($notification_type, array_column($bid->getOwner()->field_bid_notifications->getValue(), 'value'))) {
      $params = [];
      $language = ['langcode' => $bid->getOwner()->getPreferredLangcode()];
      $params['subject'] = t('You placed a bid.', [], $language);
      $params['body'][] = t('Hi, @user!', [
        '@user' => $bid->getOwner()->getDisplayName(),
      ], $language);
      $params['body'][] = t('This is an informative message about your bid in auction "@auction".', [
        '@auction' => $auction->label(),
      ], $language);
      if ($bid->autobid) {
        $params['body'][] = t(
          'Bid was made automatically following your automatic bid.',
          [],
          $language
        );
      }
      $params['body'][] = t('Your bid amount: @amount', [
        '@amount' => $auction->formatPrice($bid->getAmount(), ''),
      ], $language);
      $params['body'][] = t('Link to auction: @link', [
        '@link' => Link::fromTextAndUrl($url->toString(), $url)->toString(),
      ], $language);
      $mailer->mail('auctions', 'bid_owner_bid', $bid->getOwner()
        ->getEmail(), $bid->getOwner()->getPreferredLangcode(), $params);
    }
    // Send all followers an update.
    $followings = \Drupal::entityTypeManager()->getStorage('following')->loadByProperties([
      'auction' => $auction->id(),
    ]);
    foreach ($followings as $following) {
      $params = [];
      $follower = $following->getOwner();
      $language = ['language' => $follower->getPreferredLangcode()];
      $params['subject'] = t('New bid in auction that you follow.', [], $language);
      $params['body'][] = t('Hi, @user!', [
        '@user' => $follower->getDisplayName(),
      ], $language);
      $params['body'][] = t('Auction that you follow "@auction" has new bid.', [
        '@auction' => $auction->label(),
      ], $language);
      $params['body'][] = t('Current highest bid: @amount', [
        '@amount' => $auction->formatPrice($bid->getAmount(), ''),
      ], $language);
      $params['body'][] = t('Link to auction: @link', [
        '@link' => Link::fromTextAndUrl($url->toString(), $url)->toString(),
      ], $language);
      $mailer->mail('auctions', 'auction_follower_bid', $follower->getEmail(), $follower->getPreferredLangcode(), $params);
    }
  }

}
