<?php

namespace Drupal\auctions\Entity;

use Dr<PERSON>al\field\FieldConfigInterface;
use Drupal\Core\Datetime\DrupalDateTime;
use Drupal\Core\Entity\ContentEntityBase;
use Drupal\Core\Entity\EntityChangedTrait;
use Drupal\Core\Entity\EntityConstraintViolationList;
use Drupal\Core\Entity\EntityPublishedInterface;
use Drupal\Core\Entity\EntityPublishedTrait;
use Drupal\Core\Entity\EntityStorageInterface;
use Drupal\Core\Entity\EntityTypeInterface;
use Drupal\Core\Field\BaseFieldDefinition;
use Drupal\Core\File\FileExists;
use Drupal\Core\File\FileSystemInterface;
use Drupal\auctions\AuctionInterface;
use Drupal\auctions\BidsItemList;
use Drupal\auctions\Plugin\Field\FieldType\RemainTimeItemList;
use Drupal\datetime\Plugin\Field\FieldType\DateTimeItem;
use Drupal\file\Entity\File;
use Drupal\user\UserInterface;

/**
 * Defines the auction entity class.
 *
 * @ContentEntityType(
 *   id = "auction",
 *   label = @Translation("Auction"),
 *   label_collection = @Translation("Auctions"),
 *   bundle_label = @Translation("Auction type"),
 *   handlers = {
 *     "view_builder" = "Drupal\auctions\AuctionViewBuilder",
 *     "list_builder" = "Drupal\auctions\AuctionListBuilder",
 *     "views_data" = "Drupal\auctions\Entity\AuctionViewsData",
 *     "access" = "Drupal\auctions\AuctionAccessControlHandler",
 *     "form" = {
 *       "add" = "Drupal\auctions\Form\AuctionForm",
 *       "edit" = "Drupal\auctions\Form\AuctionForm",
 *       "default" = "Drupal\auctions\Form\AuctionForm",
 *       "submit" = "Drupal\auctions\Form\AuctionConfirmSubmitForm",
 *       "reject" = "Drupal\auctions\Form\AuctionConfirmRejectForm",
 *       "activate" = "Drupal\auctions\Form\AuctionConfirmActivateForm",
 *       "cancel" = "Drupal\auctions\Form\AuctionConfirmCancelForm",
 *       "deactivate" = "Drupal\auctions\Form\AuctionConfirmDeactivateForm",
 *       "delete" = "Drupal\Core\Entity\ContentEntityDeleteForm"
 *     },
 *     "route_provider" = {
 *       "html" = "Drupal\auctions\Routing\AuctionRouteProvider",
 *     }
 *   },
 *   base_table = "auction",
 *   data_table = "auction_field_data",
 *   translatable = TRUE,
 *   admin_permission = "administer auction types",
 *   entity_keys = {
 *     "id" = "id",
 *     "langcode" = "langcode",
 *     "bundle" = "bundle",
 *     "uuid" = "uuid"
 *   },
 *   links = {
 *     "add-form" = "/auction/add/{auction_type}",
 *     "add-page" = "/auction/add",
 *     "canonical" = "/auction/{auction}",
 *     "edit-form" = "/auction/{auction}/edit",
 *     "delete-form" = "/auction/{auction}/delete",
 *     "submit-form" = "/auction/{auction}/submit",
 *     "reject-form" = "/auction/{auction}/reject",
 *     "activate-form" = "/auction/{auction}/activate",
 *     "deactivate-form" = "/auction/{auction}/deactivate",
 *     "cancel-form" = "/auction/{auction}/cancel",
 *     "collection" = "/admin/content/auctions"
 *   },
 *   bundle_entity_type = "auction_type",
 *   field_ui_base_route = "entity.auction_type.edit_form"
 * )
 */
class Auction extends ContentEntityBase implements AuctionInterface, EntityPublishedInterface {

  use EntityChangedTrait;
  use EntityPublishedTrait;

  /**
   * {@inheritdoc}
   *
   * When a new auction entity is created, set the uid entity reference to
   * the current user as the creator of the entity.
   */
  public static function preCreate(EntityStorageInterface $storage_controller, array &$values) {
    parent::preCreate($storage_controller, $values);
    $values += ['uid' => \Drupal::currentUser()->id()];
  }

  /**
   * {@inheritdoc}
   */
  public function isPublished() {
    return $this->must_validate ?? FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function preSave(EntityStorageInterface $storage) {
    parent::preSave($storage);
    // Calculate species composition if it is not set.
    if ($this->get('field_species_composition')->isEmpty() && !$this->field_species_list->isEmpty()) {
      $this->set('field_species_composition', \Drupal::service('auctions.species_composition')->calculateSpeciesComposition($this));
    }
    if (
      ($this->get('field_price_group')->isEmpty() || $this->get('field_price_group')->value == 5) &&
      !$this->get('field_species_composition')->isEmpty()
    ) {
      $this->set('field_price_group', $this->getPriceGroup($this->get('field_species_composition')->value));
    }
    if (!$this->isNew()) {
      $this->updateCurrentPrice();
      // Set auction winner.
      if ($winning_bid = \Drupal::service('auctions.helper')->getLatestBid($this)) {
        $this->set('winner', $winning_bid->uid);
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public function postSave(EntityStorageInterface $storage, $update = TRUE) {
    parent::postSave($storage, $update);
    if (php_sapi_name() == 'cli' || $this->field_map->isEmpty() || !$this->field_map_image->isEmpty()) {
      return;
    }
    if ($this->field_map->isEmpty()) {
      return;
    }
    $point = \Drupal::service('geofield.geophp')->load($this->field_map->value)->getCentroid();
    $url = 'https://api.mapbox.com/styles/v1/guncha25/cklj7ifuc05bg17rvhh0kzlip/static/pin-m-circle+757575({LAT},{LON})/24.6,56.8,5,5,35/720x220?access_token=pk.eyJ1IjoiZ3VuY2hhMjUiLCJhIjoiY2tsYnRyaGwyMWI5djJ4bjBjZThxOXdkdCJ9.236JANJ4gKm7Pszg9E9M5w&attribution=false&logo=false';
    $url = str_replace('{LAT}', $point->coords[0], $url);
    $url = str_replace('{LON}', $point->coords[1], $url);
    $destination = 'public://auction/maps/' . DrupalDateTime::createFromTimestamp(time())->format('Y-m');
    \Drupal::service('file_system')->prepareDirectory($destination, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
    $file_url = \Drupal::service('file_system')->saveData(file_get_contents($url), $destination . DIRECTORY_SEPARATOR . $this->id() . '.png', FileExists::Replace);
    $managed_file = File::create([
      'uri' => $file_url,
      'status' => 1,
      'uid' => $this->getOwnerId(),
    ]);
    $managed_file->save();
    $this->field_map_image->target_id = $managed_file->id();
    $this->save();
  }

  /**
   * Calculates and sets price group.
   *
   * @param string $composition
   *   Species composition.
   *
   * @return int
   *   Price group id.
   */
  public static function getPriceGroup(string $composition): int {
    $composition = str_replace(' ', '', $composition ?? ' ');
    $composition_data = [];
    while (preg_match('/\d{1,2}(Ba|E|P|O|A|M|B|L)/', $composition, $data, PREG_UNMATCHED_AS_NULL)) {
      $composition_data[strtoupper($data[1])] = $composition_data[strtoupper($data[1])] ?? 0;
      $composition_data[strtoupper($data[1])] += (int) str_replace($data[1], '', $data[0]);
      $composition = str_replace($data[0], '', $composition);
    }
    $sum = 0;
    $group = ['EP' => 0, 'B' => 0, 'BA' => 0, 'Other' => 0];
    foreach ($composition_data as $type => $value) {
      $sum += $value;
      if ($type == 'E' || $type == 'P') {
        $group['EP'] += $value;
      }
      elseif ($type == 'B') {
        $group['B'] += $value;
      }
      elseif ($type == 'BA' || $type == 'A') {
        $group['BA'] += $value;
      }
      else {
        $group['Other'] += $value;
      }
    }
    if ($sum > 10 || empty($composition_data)) {
      return 4;
    }
    if ($group['EP'] >= 7) {
      return 0;
    }
    if ($group['EP'] >= 4) {
      return 1;
    }
    if ($group['B'] >= 7) {
      return 2;
    }
    return 3;
  }

  /**
   * {@inheritdoc}
   */
  public function label() {
    $label = [];

    if ($this->hasField('field_administrative_area') && !$this->get('field_administrative_area')->isEmpty()) {
      $parent = $this->entityTypeManager()
        ->getStorage('taxonomy_term')
        ->load($this->get('field_administrative_area')->target_id);
      if ($parent) {
        $label[] = $parent->label();
      }
    }
    if ($this->hasField('field_property_name') && !$this->get('field_property_name')->isEmpty()) {
      $label[] = $this->get('field_property_name')->value;
    }

    if (!empty($label)) {
      return implode(', ', $label);
    }

    return $this->id();
  }

  /**
   * {@inheritdoc}
   */
  public function getAccessToken() {
    return substr(hash('ripemd160', $this->id() . '?auction2 mod!' . $this->getOwnerId()), 0, 6);
  }

  /**
   * {@inheritdoc}
   */
  public function getCurrentPrice(bool $formatted = FALSE) {
    $price = $this->get('current_price')->value != 0
      ? $this->get('current_price')->value
      : $this->get('start_price')->value;
    return !$formatted ? $price : $this->formatPrice($price);
  }

  /**
   * {@inheritdoc}
   */
  public function formatPrice($price, $thousands_separator = ' ') {
    if ($this->isType('custom') && $this->get('field_bidding_method')->value == 'unit_price') {
      return number_format($price, 0, '.', $thousands_separator) . ' ' . strtoupper($this->currency->value) . '/' . auctions_get_auction_unit($this);
    }
    return number_format($price, 2, '.', $thousands_separator) . ' ' . strtoupper($this->currency->value);
  }

  /**
   * {@inheritdoc}
   */
  public function setCurrentPrice(float $price) {
    $this->set('current_price', $price);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function updateCurrentPrice() {
    $larger_bids_on_auction = \Drupal::database()->select('bid', 'b')
      ->fields('b', ['amount'])
      ->condition('auction', $this->id())
      ->condition('status', TRUE)
      ->orderBy('amount', 'DESC')
      ->range(0, 1)
      ->execute();
    $price = $larger_bids_on_auction->fetch()->amount ?? 0;
    return $this->setCurrentPrice((float) $price);
  }

  /**
   * {@inheritdoc}
   */
  public function hasBids() {
    return (bool) \Drupal::database()->select('bid', 'b')
      ->fields('b')
      ->condition('auction', $this->id())
      ->condition('status', TRUE)
      ->countQuery()
      ->execute()
      ->fetchField();
  }

  /**
   * {@inheritdoc}
   */
  public function getBids() {
    return $this->isNew() ? [] : $this->entityTypeManager()
      ->getStorage('bid')
      ->loadByProperties([
        'auction' => $this->id(),
        'status' => TRUE,
      ]);
  }

  /**
   * Gets list of bidders.
   *
   * @return array
   *   Bidder info.
   *
   * @throws \Drupal\Core\Entity\EntityMalformedException
   */
  public function getBidders() {
    $bidders = [];
    /** @var Bid $bid */
    foreach ($this->getBids() as $bid) {
      if (empty($bidders[$bid->getOwnerId()])) {
        $bidders[$bid->getOwnerId()] = \Drupal::currentUser()->hasPermission('change auction state')
          ? $bid->getOwner()->toLink()->toString()
          : $bid->getOwner()->getDisplayName();
      }
    }
    return $bidders;
  }

  /**
   * {@inheritdoc}
   */
  public function getMaxBid() {
    return $this->getCurrentPrice() * 5;
  }

  /**
   * {@inheritdoc}
   */
  public function getMinBid() {
    return $this->getCurrentPrice() + ($this->hasBids() ? $this->getStep() : 0);
  }

  /**
   * {@inheritdoc}
   */
  public function isInactive() {
    return $this->get('status')->value == AuctionInterface::INACTIVE;
  }

  /**
   * {@inheritdoc}
   */
  public function setInactive() {
    $this->set('status', AuctionInterface::INACTIVE);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function isActive() {
    return $this->get('status')->value == AuctionInterface::ACTIVE;
  }

  /**
   * {@inheritdoc}
   */
  public function setActive() {
    $this->set('status', AuctionInterface::ACTIVE);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function isPending() {
    return $this->get('status')->value == AuctionInterface::PENDING;
  }

  /**
   * {@inheritdoc}
   */
  public function setPending() {
    $this->set('status', AuctionInterface::PENDING);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function canBeSubmitted() {
    if ($this->isInactive() || $this->isDeclined()) {
      return TRUE;
    }
    return !($this->isPending() || $this->isActive()) && !boolval(\Drupal::service('auctions.helper')->getAuctionWinningBidUid($this));
  }

  /**
   * {@inheritdoc}
   */
  public function isFinished() {
    return $this->get('status')->value == AuctionInterface::FINISHED;
  }

  /**
   * {@inheritdoc}
   */
  public function setFinished() {
    $this->set('status', AuctionInterface::FINISHED);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function isDeclined() {
    return $this->get('status')->value == AuctionInterface::DECLINED;
  }

  /**
   * {@inheritdoc}
   */
  public function setDeclined() {
    $this->set('status', AuctionInterface::DECLINED);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function isCanceled() {
    return $this->get('status')->value == AuctionInterface::CANCELED;
  }

  /**
   * {@inheritdoc}
   */
  public function setCanceled() {
    $this->set('status', AuctionInterface::CANCELED);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getCreatedTime() {
    return $this->get('created')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setCreatedTime($timestamp) {
    $this->set('created', $timestamp);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getOwner() {
    return $this->get('uid')->entity;
  }

  /**
   * {@inheritdoc}
   */
  public function getOwnerId() {
    return $this->get('uid')->target_id;
  }

  /**
   * {@inheritdoc}
   */
  public function setOwnerId($uid) {
    $this->set('uid', $uid);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function setOwner(UserInterface $account) {
    $this->set('uid', $account->id());
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public static function baseFieldDefinitions(EntityTypeInterface $entity_type) {
    $fields = parent::baseFieldDefinitions($entity_type);

    $fields['status'] = BaseFieldDefinition::create('list_string')
      ->setLabel(t('Status'))
      ->setDefaultValue(AuctionInterface::INACTIVE)
      ->setRequired(TRUE)
      ->setSetting('allowed_values_function', 'auctions_auction_status_allowed_values_callback')
      ->setDisplayOptions('form', [
        'type' => 'options_select',
        'weight' => 0,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['description'] = BaseFieldDefinition::create('text_long')
      ->setTranslatable(TRUE)
      ->setLabel(t('Description'))
      ->setDescription(t('All relevant information that may affect the value of the auction object, such as encumbrances on entry and exit, etc.'))
      ->setDisplayOptions('form', [
        'type' => 'text_textarea',
        'weight' => 10,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('view', [
        'type' => 'text_default',
        'label' => 'above',
        'weight' => 10,
      ])
      ->setDisplayConfigurable('view', TRUE);

    $fields['uid'] = BaseFieldDefinition::create('entity_reference')
      ->setTranslatable(TRUE)
      ->setLabel(t('Author'))
      ->setDescription(t('The user ID of the auction author.'))
      ->setSetting('target_type', 'user')
      ->setDisplayOptions('form', [
        'type' => 'entity_reference_autocomplete',
        'settings' => [
          'match_operator' => 'CONTAINS',
          'size' => 60,
          'placeholder' => '',
        ],
        'weight' => 15,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'author',
        'weight' => 15,
      ])
      ->setDisplayConfigurable('view', TRUE);

    $fields['created'] = BaseFieldDefinition::create('created')
      ->setLabel(t('Authored on'))
      ->setTranslatable(TRUE)
      ->setDescription(t('The time that the auction was created.'))
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'timestamp',
        'weight' => 20,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('form', [
        'type' => 'datetime_timestamp',
        'weight' => 20,
      ])
      ->setDisplayConfigurable('view', TRUE);

    $fields['changed'] = BaseFieldDefinition::create('changed')
      ->setLabel(t('Changed'))
      ->setTranslatable(TRUE)
      ->setDescription(t('The time that the auction was last edited.'));

    $fields['end_time'] = BaseFieldDefinition::create('datetime')
      ->setLabel(t('End time'))
      ->setTranslatable(FALSE)
      ->setRequired(TRUE)
      ->setDefaultValueCallback('auctions_end_time_callback')
      ->setSetting('datetime_type', DateTimeItem::DATETIME_TYPE_DATETIME)
      ->setDescription(t('We recommend setting an end time with a calculation so that potential buyers have the opportunity to get acquainted with the felling in nature, a minimum of 1 (one week). The end time should also be during working hours.'))
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['start_price'] = BaseFieldDefinition::create('float')
      ->setLabel(t('Starting price'))
      ->setDescription(t('Auction start price - the minimum price at which the first bidder can buy.'))
      ->setRequired(TRUE)
      ->setSetting('min', 1)
      ->setDisplayOptions('form', [
        'type' => 'step_number',
        'weight' => -10,
      ])
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'number_integer',
        'weight' => -10,
      ])
      ->setDefaultValue(10000)
      ->addConstraint('PositiveNumber')
      ->addConstraint('MaxPrice', ['max' => 5000000])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['current_price'] = BaseFieldDefinition::create('float')
      ->setLabel(t('Current price'))
      ->setDescription(t('Current price of auction'))
      ->setRequired(TRUE)
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'number_integer',
        'weight' => -10,
      ])
      ->setDefaultValue(0)
      ->addConstraint('PositiveNumber')
      ->addConstraint('MaxPrice', ['max' => 1000000])
      ->setDisplayConfigurable('view', TRUE);

    $fields['currency'] = BaseFieldDefinition::create('list_string')
      ->setLabel(t('Currency'))
      ->setDefaultValue('EUR')
      ->setRequired(TRUE)
      ->setSetting('allowed_values_function', 'auctions_auction_currency_allowed_values_callback')
      ->setDisplayOptions('form', [
        'type' => 'options_select',
        'weight' => 0,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['path'] = BaseFieldDefinition::create('path')
      ->setLabel(t('URL alias'))
      ->setDescription(t('The auction URL alias.'))
      ->setTranslatable(TRUE)
      ->setDisplayConfigurable('form', FALSE)
      ->setComputed(TRUE);

    $fields['bids'] = BaseFieldDefinition::create('auctions_bids')
      ->setLabel(t('Bids'))
      ->setDisplayConfigurable('form', FALSE)
      ->setDisplayConfigurable('view', TRUE)
      ->setClass(BidsItemList::class)
      ->setComputed(TRUE);

    $fields['winner'] = BaseFieldDefinition::create('entity_reference')
      ->setTranslatable(TRUE)
      ->setLabel(t('Winner'))
      ->setDescription(t('The user ID of the auction winner.'))
      ->setSetting('target_type', 'user')
      ->setRequired(FALSE)
      ->setDisplayConfigurable('form', FALSE)
      ->setDisplayConfigurable('view', FALSE);

    $fields['remain_time'] = BaseFieldDefinition::create('datetime')
      ->setName('remain_time')
      ->setLabel(t('Time left'))
      ->setComputed(TRUE)
      ->setClass(RemainTimeItemList::class)
      ->setDisplayConfigurable('view', TRUE);

    return $fields;
  }

  /**
   * Builds auction owner block.
   *
   * @param array $build
   *   Array to build.
   *
   * @throws \Drupal\Core\Entity\EntityMalformedException
   */
  public function getAuctionOwnerBlock(array &$build) {
    if (
      ($this->isActive() && \Drupal::currentUser()->id() != $this->getOwnerId()) ||
      ($this->isFinished() && \Drupal::service('auctions.helper')->getAuctionWinningBidUid($this) == \Drupal::currentUser()->id()) ||
      (\Drupal::currentUser()->hasPermission('access auction overview'))
    ) {
      $build['member_for'] = [
        '#type' => 'container',
        '#attributes' => [
          'id' => 'member_for',
        ],
      ];
      $build['member_for']['display_name'] = [
        '#theme' => 'mb_field',
        '#label' => t('User'),
        '#value' => array_intersect(['editor', 'administrator'], \Drupal::currentUser()->getRoles()) ? $this->getOwner()->toLink() : $this->getOwner()->getDisplayName(),
        '#attributes' => [
          'class' => ['field--name-field-contact-information'],
        ],
      ];
      $build['member_for']['member_for'] = [
        '#theme' => 'mb_field',
        '#label' => t('Member for'),
        '#value' => \Drupal::service('date.formatter')->formatTimeDiffSince($this->getOwner()->getCreatedTime()),
        '#attributes' => [
          'class' => ['field--name-field-contact-information'],
        ],
      ];
      $build['member_for']['total_auctions'] = [
        '#theme' => 'mb_field',
        '#label' => t('User auctions'),
        '#value' => \Drupal::service('auctions.helper')->getUserAuctionCount($this->getOwner()),
        '#attributes' => [
          'class' => ['field--name-field-contact-information'],
        ],
      ];
      $build['member_for']['email'] = [
        '#theme' => 'mb_field',
        '#label' => t('Email'),
        '#value' => $this->getOwner()->get('mail')->value,
        '#attributes' => [
          'class' => ['field--name-field-contact-information'],
        ],
        '#access' => $this->isFinished() && \Drupal::service('auctions.helper')->getAuctionWinningBidUid($this) == \Drupal::currentUser()->id(),
      ];
      $build['member_for']['phone'] = [
        '#theme' => 'mb_field',
        '#label' => t('Phone number'),
        '#value' => $this->getOwner()->get('field_phone')->value,
        '#attributes' => [
          'class' => ['field--name-field-contact-information'],
        ],
        '#access' => $this->isFinished() && \Drupal::service('auctions.helper')->getAuctionWinningBidUid($this) == \Drupal::currentUser()->id(),
      ];
    }
  }

  /**
   * {@inheritdoc}
   */
  public function validate() {
    $this->validated = TRUE;
    $entity = $this->getTypedData()->getEntity();

    $field_validation_disabled = [
      'field_quantity',
      'field_object',
    ];

    if ($this->isType('property')) {
      $field_validation_disabled = [
        'field_cutting_area',
        'field_cutting_type',
        'field_forest_volume',
        'field_forwarding_distance',
        'field_species_composition',
        'field_route_and_strorage',
        'field_quantity',
        'field_object',
        'field_bidding_method',
        'field_step',
      ];
    }
    elseif ($this->isType('felling')) {
      $field_validation_disabled = [
        'field_property_area_size',
        'field_property_forest_size',
        'field_property_forest_condition',
        'field_total_forest_stock',
        'field_quantity',
        'field_object',
        'field_bidding_method',
        'field_step',
      ];
    }
    elseif ($this->isType('custom')) {
      $field_validation_disabled = [
        'field_property_area_size',
        'field_property_forest_size',
        'field_property_forest_condition',
        'field_total_forest_stock',
        'field_cutting_type',
        'field_forest_volume',
        'field_cutting_area',
        'field_ownership',
        'field_owner_confirmation_file',
        'field_forwarding_distance',
        'field_route_and_strorage',
        'field_measurements_not_made',
        'field_delivery_conditions',
        'field_species_composition',
        'field_price_group',
        'field_species_list',
        'field_felling_certificate_num',
        'field_felling_certificate_files',
        'field_agreement_type',
        'field_agreement_signing',
        'field_purchase_agreement',
      ];
    }

    // Check if current user has editor or administrator role.
    $current_user = \Drupal::currentUser();
    $is_editor = !empty(array_intersect(['editor', 'administrator'], $current_user->getRoles()));

    // If user is editor, disable require_on_publish validation
    // only for field_total_forest_stock.
    if ($is_editor && isset($entity->getFields()['field_total_forest_stock'])) {
      $field = $entity->getFields()['field_total_forest_stock'];
      $field_definition = $field->getFieldDefinition();
      if ($field_definition instanceof FieldConfigInterface) {
        $field_definition->setThirdPartySetting('require_on_publish', 'require_on_publish', FALSE);
        $field_definition->setThirdPartySetting('require_on_publish', 'warn_on_empty', FALSE);
      }
    }

    // Apply the regular field validation disabling based on auction type.
    foreach ($field_validation_disabled as $field) {
      if (isset($entity->getFields()[$field])) {
        $entity->getFields()[$field]->getFieldDefinition()->setThirdPartySetting('require_on_publish', 'require_on_publish', FALSE);
        $entity->getFields()[$field]->getFieldDefinition()->setThirdPartySetting('require_on_publish', 'warn_on_empty', FALSE);
        $entity->getFields()[$field]->getFieldDefinition()->setRequired(FALSE);
      }
    }

    $violations = $this->getTypedData()->validate();
    return new EntityConstraintViolationList($this, iterator_to_array($violations));
  }

  /**
   * {@inheritdoc}
   */
  public function getAuctionType() {
    return $this->get('field_auction_type')?->value;
  }

  /**
   * {@inheritdoc}
   */
  public function isType(string $type): bool {
    return $this->getAuctionType() == $type;
  }

  /**
   * {@inheritdoc}
   */
  public function isPrivate(): bool {
    return boolval($this->get('field_private')->value);
  }

  /**
   * {@inheritdoc}
   */
  public function getStep(): float {
    return (float) ($this->get('field_step')->isEmpty() ? 100 : $this->get('field_step')->value);
  }

}
