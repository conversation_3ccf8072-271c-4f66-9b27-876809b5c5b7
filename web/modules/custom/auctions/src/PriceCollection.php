<?php

namespace Drupal\auctions;

/**
 * PriceCollection class that describes min, max, avg price.
 */
class PriceCollection {

  /**
   * Minimum price for collection.
   *
   * @var float
   */
  protected $minimum;

  /**
   * Maximum price for collection.
   *
   * @var float
   */
  protected $maximum;

  /**
   * Volume for collection.
   *
   * @var float
   */
  protected $volume;

  /**
   * Price for collection.
   *
   * @var float
   */
  protected $price;

  /**
   * Auctions.
   *
   * @var \Drupal\auctions\AuctionInterface[]
   */
  private $auctions;

  /**
   * Constructs PriceCollection.
   *
   * @param float $minimum
   *   Minimum price for collection.
   * @param float $maximum
   *   Maximum price for collection.
   * @param float $volume
   *   Volume for collection.
   * @param float $price
   *   Price for collection.
   */
  public function __construct(float $minimum = 0, float $maximum = 0, float $volume = 0, float $price = 0) {
    $this->minimum = $minimum;
    $this->maximum = $maximum;
    $this->volume = $volume;
    $this->price = $price;
  }

  /**
   * Add auction.
   *
   * @param \Drupal\auctions\AuctionInterface $auction
   *   Auction.
   */
  public function addAuction(AuctionInterface $auction): void {
    $this->auctions[] = $auction;
  }

  /**
   * Gets auctions.
   *
   * @return \Drupal\auctions\AuctionInterface[]
   *   Auctions.
   */
  public function getAuctions(): array {
    return $this->auctions;
  }

  /**
   * Sets volume.
   *
   * @param float $volume
   *   New volume.
   */
  public function setVolume(float $volume): void {
    $this->volume = $volume;
  }

  /**
   * Sets price.
   *
   * @param float $price
   *   New price.
   */
  public function setPrice(float $price): void {
    $this->price = $price;
  }

  /**
   * Adds to volume.
   *
   * @param float $volume
   *   Volume to add.
   *
   * @return \Drupal\auctions\PriceCollection
   *   Self.
   */
  public function addVolume(float $volume): PriceCollection {
    $this->setVolume($this->volume + $volume);
    return $this;
  }

  /**
   * Adds to price.
   *
   * @param float $price
   *   Price to add.
   *
   * @return \Drupal\auctions\PriceCollection
   *   Self.
   */
  public function addPrice(float $price): PriceCollection {
    $this->setPrice($this->price + $price);
    return $this;
  }

  /**
   * Gets maximum price.
   *
   * @return float
   *   Maximum.
   */
  public function getMaximum(): float {
    return $this->maximum;
  }

  /**
   * Gets average price.
   *
   * @return float
   *   Average.
   */
  public function getAverage(): float {
    return $this->volume != 0 ? $this->price / $this->volume : 0;
  }

  /**
   * Gets minimum price.
   *
   * @return float
   *   Minimum.
   */
  public function getMinimum(): float {
    return $this->minimum;
  }

  /**
   * Sets minimum price.
   *
   * @param float $minimum
   *   Min price.
   */
  public function setMinimum(float $minimum): void {
    $this->minimum = $minimum;
  }

  /**
   * Sets maximum price.
   *
   * @param float $maximum
   *   Max price.
   */
  public function setMaximum(float $maximum): void {
    $this->maximum = $maximum;
  }

}
