<?php

namespace Drupal\auctions\Form;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\Core\Routing\RouteMatchInterface;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;

/**
 * Class AuctionSubscribeForm for subscribing to auction.
 *
 * @package Drupal\auctions\Form
 */
class AuctionSubscribeForm extends FormBase {

  /**
   * Auction entity.
   *
   * @var \Drupal\auctions\Entity\Auction
   */
  public $auction;

  /**
   * The following storage.
   *
   * @var \Drupal\Core\Entity\EntityStorageInterface
   */
  public $followingStorage;

  /**
   * AuctionSubscribeForm constructor.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Routing\RouteMatchInterface $route_match
   *   The route match.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, RouteMatchInterface $route_match) {
    $this->followingStorage = $entity_type_manager->getStorage('following');
    $this->routeMatch = $route_match;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('current_route_match')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'auctions_auction_subscribe';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    if (!$this->auction = $this->routeMatch->getParameter('auction')) {
      return [];
    }
    $isFollowing = $this->getFollowing();
    $id = 'subscribe-to-auction-' . $this->auction->id();
    $form['subscribe'] = [
      '#type' => 'submit',
      '#value' => $isFollowing ? $this->t('Un-follow') : $this->t('Follow'),
      '#attributes' => [
        'class' => [
          'use-ajax',
          'button--action',
          'button--follow',
          $isFollowing ? 'following' : '',
        ],
        'data-twig-suggestion' => 'button_follow',
      ],
      '#prefix' => '<div id="' . $id . '">',
      '#suffix' => '</div>',
      '#ajax' => [
        'callback' => [$this, 'ajaxSubmitForm'],
        'event' => 'click',
        'wrapper' => $id,
        'effect' => 'fade',
        'progress' => [
          'type' => 'none',
        ],
      ],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {}

  /**
   * {@inheritdoc}
   */
  public function ajaxSubmitForm(array &$form, FormStateInterface $form_state) {
    if ($following = $this->getFollowing()) {
      $following->delete();
      $form['subscribe']['#value'] = $this->t('Follow');
      $classes = &$form['subscribe']['#attributes']['class'];
      $classes = array_filter($classes, function ($e) {
        return $e != 'following';
      });
    }
    else {
      $this->followingStorage->create([
        'uid' => $this->currentUser()->id(),
        'auction' => $this->auction->id(),
      ])->save();
      $form['subscribe']['#value'] = $this->t('Un-follow');
      $form['subscribe']['#attributes']['class'][] = 'following';
    }
    return $form['subscribe'];
  }

  /**
   * Gets following entity.
   *
   * @return bool|\Drupal\Core\Entity\EntityInterface
   *   Following entity or FALSE.
   */
  public function getFollowing() {
    $following = $this->followingStorage->loadByProperties([
      'uid' => $this->currentUser()->id(),
      'auction' => $this->auction->id(),
    ]);
    return $following ? reset($following) : FALSE;
  }

}
