<?php

namespace Drupal\auctions\Form;

use <PERSON>upal\Core\Form\FormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON>upal\Core\State\StateInterface;
use Drupal\file\Entity\File;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides an Auctions agreement form settings.
 */
class DefaultAgreementForm extends FormBase {

  /**
   * The state key/value collection.
   *
   * @var \Drupal\Core\State\StateInterface
   */
  protected StateInterface $state;

  /**
   * Constructs a new DefaultAgreementForm object.
   *
   * @param \Drupal\Core\State\StateInterface $state
   *   State Service Object.
   */
  public function __construct(StateInterface $state) {
    $this->state = $state;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('state')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId(): string {
    return 'auctions_default_agreement';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $fid = $this->state->get('auctions_default_agreement_file');
    $fid_legal = $this->state->get('auctions_default_agreement_file_legal_entity');

    $form['file'] = [
      '#title' => $this->t('Default agreement'),
      '#type' => 'managed_file',
      '#upload_location' => 'private://auction_agreement',
      '#default_value' => $fid ? [$fid] : NULL,
      '#upload_validators' => [
        'file_validate_extensions' => ['pdf'],
      ],
    ];

    $form['file_legal_entity'] = [
      '#title' => $this->t('Default agreement for legal entity'),
      '#type' => 'managed_file',
      '#upload_location' => 'private://auction_agreement',
      '#default_value' => $fid_legal ? [$fid_legal] : NULL,
      '#upload_validators' => [
        'file_validate_extensions' => ['pdf'],
      ],
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Save'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->messenger()->addStatus($this->t('Settings saved'));
    if ($form_state->getValue(['file', 0])) {
      $file = File::load($form_state->getValue(['file', 0]));
      $file->setPermanent();
      $file->save();
    }
    if ($form_state->getValue(['file_legal_entity', 0])) {
      $file = File::load($form_state->getValue(['file_legal_entity', 0]));
      $file->setPermanent();
      $file->save();
    }
    $this->state->set('auctions_default_agreement_file', $form_state->getValue(['file', 0]));
    $this->state->set('auctions_default_agreement_file_legal_entity', $form_state->getValue(['file_legal_entity', 0]));
  }

}
