<?php

namespace Drupal\auctions\Form;

use Drupal\Core\Entity\ContentEntityForm;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Url;

/**
 * Class AuctionConfirmDeactivateForm to deactivate auction.
 *
 * @package Drupal\auctions\Form
 */
class AuctionConfirmDeactivateForm extends ContentEntityForm {

  /**
   * Provides a generic edit title callback.
   *
   * @return string|null
   *   The title for the entity edit page, if an entity was found.
   */
  public function submitTitle() {
    if ($entity = $this->getRouteMatch()->getParameter('auction')) {
      return $this->t('Deactivate auction %label.', ['%label' => $entity->label()]);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'auctions_confirm_deactivate';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['message'] = [
      '#type' => 'html_tag',
      '#tag' => 'p',
      '#value' => $this->t('This action will deactivate auction @label.', ['@label' => $this->entity->label()]),
    ];
    $form['actions'] = [
      '#type' => 'actions',
      'submit' => [
        '#type' => 'submit',
        '#value' => $this->t('Deactivate'),
      ],
    ];
    $form['actions']['cancel'] = [
      '#title' => $this->t('Cancel'),
      '#type' => 'link',
      '#url' => $this->getCancelUrl(),
      '#attributes' => ['class' => ['button', 'button--danger']],
    ];

    return $form;
  }

  /**
   * Gets cancel action url.
   *
   * @return \Drupal\Core\Url
   *   Cancellation url.
   */
  public function getCancelUrl() {
    if ($destination = \Drupal::destination()->get()) {
      return Url::fromUserInput($destination);
    }
    return new Url('entity.auction.canonical', ['auction' => $this->entity]);
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    if (!($this->entity->access('update') || \Drupal::currentUser()->hasPermission('change auction state')) ||
        $this->entity->hasBids() ||
        !($this->entity->isFinished() || $this->entity->isActive())
      ) {
      $form_state->setError($form, t('You do not have permissions to deactivate this auction.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->entity->setInactive()->save();
    $this->messenger()->addStatus($this->t('Auction deactivated.'));
    $form_state->setRedirectUrl($this->getCancelUrl());
  }

}
