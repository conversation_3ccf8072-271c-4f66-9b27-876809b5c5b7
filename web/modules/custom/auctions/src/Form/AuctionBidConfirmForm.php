<?php

namespace Drupal\auctions\Form;

use Drupal\Core\Access\AccessResult;
use Drupal\Core\Ajax\AjaxResponse;
use Drupal\Core\Ajax\CloseDialogCommand;
use Drupal\Core\Ajax\CloseModalDialogCommand;
use <PERSON>upal\Core\Ajax\HtmlCommand;
use Drupal\Core\Ajax\MessageCommand;
use Drupal\Core\Ajax\OpenModalDialogCommand;
use Drupal\Core\Ajax\RedirectCommand;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormBuilderInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Link;
use Drupal\Core\Messenger\MessengerInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Session\AccountProxyInterface;
use Drupal\Core\Url;
use Drupal\auctions\AuctionInterface;
use Drupal\auctions\AuctionsHelper;
use Drupal\auctions\AutoBidInterface;
use Drupal\auctions\BidInterface;
use Drupal\auctions\Entity\Auction;
use Drupal\auctions\Entity\Bid;
use Drupal\userpoints\TransactionHandler;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Builds auction bid confirm form.
 *
 * @package Drupal\auctions\Form
 */
class AuctionBidConfirmForm extends FormBase {

  /**
   * The transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  protected $transactionHandler;

  /**
   * The bid.
   *
   * @var \Drupal\auctions\BidInterface
   */
  protected $bid;


  /**
   * The form builder.
   *
   * @var \Drupal\Core\Form\FormBuilderInterface
   */
  protected $formBuilder;

  /**
   * The auction.
   *
   * @var \Drupal\auctions\AuctionInterface
   */
  protected $auction;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountProxyInterface
   */
  protected $currentUser;

  /**
   * The auctions helper.
   *
   * @var \Drupal\auctions\AuctionsHelper
   */
  protected $auctionsHelper;

  /**
   * The bid storage.
   *
   * @var \Drupal\Core\Entity\EntityStorageInterface
   */
  protected $bidStorage;

  /**
   * Constructs a new AuctionBidForm.
   *
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   * @param \Drupal\Core\Form\FormBuilderInterface $form_builder
   *   The form builder.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The form builder.
   * @param \Drupal\Core\Session\AccountProxyInterface $account_proxy
   *   The form builder.
   * @param \Drupal\auctions\AuctionsHelper $auctions_helper
   *   The auctions helper.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  public function __construct(
    TransactionHandler $transaction_handler,
    FormBuilderInterface $form_builder,
    EntityTypeManagerInterface $entity_type_manager,
    AccountProxyInterface $account_proxy,
    AuctionsHelper $auctions_helper,
  ) {
    $this->transactionHandler = $transaction_handler;
    $this->formBuilder = $form_builder;
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $account_proxy;
    $this->auctionsHelper = $auctions_helper;
    $this->bidStorage = $this->entityTypeManager->getStorage('bid');
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('userpoints.transaction_handler'),
      $container->get('form_builder'),
      $container->get('entity_type.manager'),
      $container->get('current_user'),
      $container->get('auctions.helper')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'auctions_auction_bid_confirm';
  }

  /**
   * Checks access for a form request.
   *
   * @param \Drupal\Core\Session\AccountInterface $account
   *   Run access checks for this account.
   *
   * @return \Drupal\Core\Access\AccessResultInterface
   *   The access result.
   */
  public function access(AccountInterface $account) {
    return AccessResult::allowedIf($this->getRequest()->isXmlHttpRequest());
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, ?AuctionInterface $auction = NULL, $bid = NULL) {
    $form['#cache']['max-age'] = 0;
    $form['#attached']['library'][] = 'auctions/number-input';
    // Set bid and auction if not set on object.
    if (!$this->bid || !$this->auction) {
      $this->auction = $auction;
      $this->bid = $this->bidStorage->create([
        'amount' => $bid ?? $form_state->getUserInput()['bid'],
        'auction' => $this->auction->id(),
        'uid' => $this->currentUser->id(),
        'currency' => $this->auction->currency->value,
      ]);
    }
    $this->auction = Auction::load($this->auction->id());
    // Update bid amount.
    if ($this->bid->getAmount() != $form_state->getUserInput()['bid'] && $form_state->getUserInput()['bid']) {
      $this->bid->setAmount($form_state->getUserInput()['bid']);
    }
    // Return error message if auction has ended or not valid auction.
    if (!$this->auction instanceof AuctionInterface || !$this->auction->isActive()) {
      $response = new AjaxResponse();
      $response->addCommand(new CloseModalDialogCommand());
      $response->addCommand(new HtmlCommand('.auctions-auction-bid', ''));
      $response->addCommand(new HtmlCommand('.messages__wrapper', ''));
      $response->addCommand(new MessageCommand($this->t('Auction has ended.'), NULL, ['type' => 'error']));
      return $response;
    }
    $fee = userpoints_get_fee(AUCTIONS_BID_AUCTION_OP, $this->bid);
    $errors = [];
    if ($this->bid->getAmount() < $this->auction->getMinBid()) {
      $errors[] = $this->t('Minimum bid should be at least @bid', ['@bid' => $this->auction->getMinBid()]);
    }
    elseif ($this->bid->getAmount() > $this->auction->getMaxBid()) {
      $errors[] = $this->t('Maximum bid amount should be @bid', ['@bid' => $this->auction->getMaxBid()]);
    }
    elseif (fmod(round((float) $this->bid->getAmount() - $this->auction->getMinBid(), 2), $this->auction->getStep()) != 0.00) {
      $errors[] = $this->t('Bid step should be @step @currency', [
        '@step' => $this->auction->getStep(),
        '@currency' => $this->auction->currency->value,
      ]);
    }
    elseif (!$this->transactionHandler->userIsSolventToPay($this->currentUser, $fee)) {
      $errors[] = $this->t('You have insufficient funds to make this bid.');
      $errors[] = Link::createFromRoute($this->t('Click here to purchase points!'), 'userpoints.point_purchase');
    }
    else {
      // If there is no error build bid as hidden.
      $form['bid'] = [
        '#type' => 'hidden',
        '#value' => $this->bid->getAmount(),
      ];
      $form['confirm_bid'] = [
        '#type' => 'html_tag',
        '#tag' => 'h3',
        '#value' => $this->t('Your bid: @bid', ['@bid' => $this->bid->getAmount()]),
      ];
    }

    // Add message area.
    $form['message'] = [
      '#type' => 'status_messages',
    ];

    // If there is an error build bid as input.
    if (!empty($errors)) {
      foreach ($errors as $error) {
        $this->messenger()->addError($error);
      }
      $form['bid'] = [
        '#type' => 'number',
        '#title' => $this->t('Your bid'),
        '#default_value' => number_format($form_state->getUserInput()['bid'] ?? $this->auction->getMinBid(), 2, '.', ' '),
        '#min' => $this->auction->getMinBid(),
        '#max' => $this->auction->getMaxBid(),
        '#step' => 100.00,
        '#required' => TRUE,
        '#attributes' => [
          'class' => ['round-' . $this->auction->getStep()],
          'autocomplete' => 'off',
        ],
      ];
    }

    $is_free_auction = $fee == 0;

    // Display userpoints.
    $form['userpoints'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $this->t('Your balance is: @points', [
        '@points' => $this->transactionHandler->getCurrentUserBalance(),
      ]),
      '#access' => !$is_free_auction,
    ];

    // Display fee.
    $form['fee'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#value' => $this->auction->getMinBid() == intval($this->bid->getAmount())
        ? $this->t('Maximum commission fee (Without VAT): @fee EUR', [
          '@fee' => $fee,
        ])
        : $this->t('Current commission fee (Without VAT): @fee EUR', [
          '@fee' => $fee,
        ]),
      '#access' => !$is_free_auction,
    ];

    $form['actions'] = [
      '#type' => 'actions',
      'confirm' => [
        '#type' => 'submit',
        '#value' => $this->t('Confirm bid'),
        '#attributes' => [
          'class' => [
            'use-ajax',
          ],
        ],
        '#ajax' => [
          'callback' => [$this, 'ajaxSubmitForm'],
          'event' => 'click',
          'url' => Url::fromRoute('auctions.bid_confirm', [
            'auction' => $this->auction->id(),
          ]),
          'options' => [
            'query' => [
              'ajax_form' => 1,
            ],
          ],
        ],
      ],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $form_state->clearErrors();
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $form_state->clearErrors();
  }

  /**
   * {@inheritdoc}
   */
  public function ajaxSubmitForm(array &$form, FormStateInterface $form_state) {
    $this->logger('auction_bid')->info(
      $this->t('Auction bid confirm attempted by @user. With bid: @bid', [
        '@bid' => $this->bid->getAmount(),
        '@user' => $this->currentUser()->getDisplayName(),
      ]));
    // Lock bidding.
    if (!\Drupal::lock()->lockMayBeAvailable('auction_action')) {
      \Drupal::lock()->wait('auction_action');
    }
    if (!\Drupal::lock()->acquire('auction_action', 30.0)) {
      return $this->ajaxSubmitForm($form, $form_state);
    }
    $storage = $this->entityTypeManager->getStorage('auction');
    $storage->resetCache([$this->auction->id()]);
    $this->auction = $storage->load($this->auction->id());
    $response = new AjaxResponse();
    // Check if form needs to be rebuilt.
    $rebuild_modal = $this->bid->getAmount() != $form_state->getValue('bid');
    $fee = userpoints_get_fee(AUCTIONS_BID_AUCTION_OP, $this->bid);
    $rebuild_modal = !$this->auction->isActive() ? TRUE : $rebuild_modal;
    $rebuild_modal = $form_state->getValue('bid') < $this->auction->getMinBid() ? TRUE : $rebuild_modal;
    $rebuild_modal = $form_state->getValue('bid') > $this->auction->getMaxBid() ? TRUE : $rebuild_modal;
    $rebuild_modal = fmod(round($form_state->getValue('bid') - $this->auction->getMinBid(), 2), $this->auction->getStep()) != 0.00 ? TRUE : $rebuild_modal;
    $rebuild_modal = !$this->transactionHandler->userIsSolventToPay($this->currentUser, $fee) ? TRUE : $rebuild_modal;
    // Rebuild form with same form state if needed.
    if ($rebuild_modal) {
      $response->addCommand(new CloseDialogCommand());
      $modal_form = $this->formBuilder->rebuildForm($form_state->getBuildInfo()['form_id'], $form_state, $this->buildForm($form, $form_state));
      $response->addCommand(
        new OpenModalDialogCommand(
          $this->t('Confirm bid for @auction', ['@auction' => $this->auction->label()]),
          $modal_form,
          ['width' => '800']
        )
      );
      // Unlock bidding.
      \Drupal::lock()->release('auction_action');
      return $response;
    }
    // Replace bid with autobid if bid is bigger than a 1 step.
    if ($this->auction->getMinBid() + $this->auction->getStep() <= $this->bid->getAmount()) {
      $this->bid = $this->createAutoBid($this->bid);
    }
    $old_bid = $this->auctionsHelper->getLatestBid($this->auction);
    try {
      $this->bid->setCreatedTime(time());
      $this->bid->save();
      if ($this->bid instanceof BidInterface) {
        $msg = $this->t('Your bid was saved.');
        $this->auctionsHelper->autobid($this->auction->id());
      }
      elseif ($this->bid instanceof AutoBidInterface) {
        $msg = $this->t('Your auto bid was saved.');
      }
      $msg_type = MessengerInterface::TYPE_STATUS;
      // Invoke hook to inform about bid status changes.
      $new_bid = $this->auctionsHelper->getLatestBid($this->auction);
      if ($old_bid && $new_bid) {
        \Drupal::moduleHandler()->invokeAll('auction_bid_made', [
          $this->bidStorage->load($old_bid->id),
          $this->bidStorage->load($new_bid->id),
          $this->bid,
        ]);
      }
    }
    catch (\Exception $exception) {
      $msg_type = MessengerInterface::TYPE_ERROR;
    }

    // Unlock bidding.
    \Drupal::lock()->release('auction_action');

    $response->addCommand(
      new RedirectCommand(
        Url::fromRoute('entity.auction.canonical', [
          'auction' => $this->auction->id(),
        ])->toString()
      )
    );
    if ($msg_type != MessengerInterface::TYPE_ERROR) {
      $this->messenger()->addMessage($msg, $msg_type);
    }

    return $response;
  }

  /**
   * Creates or loads autobid.
   *
   * @param \Drupal\auctions\BidInterface $bid
   *   Bid.
   *
   * @return \Drupal\auctions\AutoBidInterface|\Drupal\Core\Entity\EntityInterface
   *   Auto bid.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  protected function createAutoBid(BidInterface $bid) {
    /** @var \Drupal\auctions\AutoBidInterface[] $auto_bids */
    $auto_bids = $this->entityTypeManager
      ->getStorage('auto_bid')
      ->loadByProperties([
        'uid' => $this->currentUser()->id(),
        'auction' => $this->auction->id(),
      ]);
    if ($auto_bids) {
      return reset($auto_bids)
        ->setStatus(TRUE)
        ->set('amount', $bid->getAmount())
        ->set('hostname', Bid::getDefaultHostname())
        ->setCreatedTime(\Drupal::time()->getCurrentTime());
    }
    return $this->entityTypeManager->getStorage('auto_bid')->create([
      'amount' => $bid->getAmount(),
      'uid' => $this->currentUser()->id(),
      'auction' => $bid->getAuctionId(),
    ]);
  }

}
