<?php

namespace Drupal\auctions;

use Drupal\Core\Access\AccessResult;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\file\FileAccessControlHandler;
use Drupal\file\FileAccessFormatterControlHandlerInterface;

/**
 * Provides a File access control handler.
 */
class FileViewAccessControlHandler extends FileAccessControlHandler implements FileAccessFormatterControlHandlerInterface {

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    if ($operation == 'view' && $account->isAnonymous() && \Drupal::service('file_system')->uriScheme($entity->getFileUri()) === 'private') {
      return AccessResult::forbidden();
    }
    return parent::checkAccess($entity, $operation, $account);
  }

}
