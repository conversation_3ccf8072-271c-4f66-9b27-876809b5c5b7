<?php

namespace Drupal\auctions;

use <PERSON>upal\Core\Access\AccessResult;
use Drupal\Core\Entity\EntityAccessControlHandler;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;

/**
 * Defines the access control handler for the auto-bid entity type.
 */
class AutoBidAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    /** @var \Drupal\auctions\AuctionInterface $entity */
    switch ($operation) {
      case 'update':
        return AccessResult::allowedIf($account->hasPermission('edit auto_bid'));

      case 'delete':
        return AccessResult::allowedIf($account->hasPermission('delete auto_bid'));

      default:
        // No opinion.
        return AccessResult::neutral();
    }
  }

}
