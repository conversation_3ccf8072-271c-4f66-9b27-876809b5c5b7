<?php

namespace Drupal\auctions;

use Dr<PERSON><PERSON>\Core\Entity\ContentEntityInterface;
use <PERSON><PERSON><PERSON>\user\EntityOwnerInterface;

/**
 * Provides an interface defining a bid entity type.
 */
interface BidInterface extends ContentEntityInterface, EntityOwnerInterface {

  /**
   * Gets the bid creation timestamp.
   *
   * @return int
   *   Creation timestamp of the bid.
   */
  public function getCreatedTime();

  /**
   * Sets the bid creation timestamp.
   *
   * @param int $timestamp
   *   The bid creation timestamp.
   *
   * @return \Drupal\auctions\BidInterface
   *   The called bid entity.
   */
  public function setCreatedTime($timestamp);

  /**
   * Returns the bid status.
   *
   * @return bool
   *   TRUE if the bid is enabled, FALSE otherwise.
   */
  public function isEnabled();

  /**
   * Sets the bid status.
   *
   * @param bool $status
   *   TRUE to enable this bid, FALSE to disable.
   *
   * @return \Drupal\auctions\BidInterface
   *   The called bid entity.
   */
  public function setStatus($status);

  /**
   * Gets bid amount.
   *
   * @return float
   *   Bid amount.
   */
  public function getAmount();

  /**
   * Sets the bid amount.
   *
   * @param float $amount
   *   Bid amount.
   *
   * @return \Drupal\auctions\BidInterface
   *   The called bid entity.
   */
  public function setAmount(float $amount);

  /**
   * Gets bid auction.
   *
   * @return \Drupal\auctions\AuctionInterface
   *   Auction.
   */
  public function getAuction();

  /**
   * Gets bid auction id.
   *
   * @return int
   *   Auction id.
   */
  public function getAuctionId();

}
