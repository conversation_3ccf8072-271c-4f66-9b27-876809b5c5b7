entity.auction_type.collection:
  title: 'Auction types'
  parent: system.admin_structure
  description: 'Manage and CRUD actions on Auction type.'
  route_name: entity.auction_type.collection
entity.bid.settings:
  title: Bids
  description: Configure a bids
  route_name:  entity.bid.settings
  parent: system.admin_structure

auctions.auction_newsletter_settings:
  title: Auction newsletter settings
  parent: system.admin_config_system
  route_name: auctions.auction_newsletter_settings
  weight: 10

auctions.kpi:
  title: 'KPI'
  route_name: view.kpis.page
  parent: system.admin_content
  weight: 50
auctions.user_bids:
  title: 'User participated auctions'
  route_name: view.user_auctions_bid.overview
  parent: entity.user.collection
  weight: -20

auctions.default_agreement:
  title: 'Default agreement'
  route_name: auctions.default_agreement
  description: 'Auction default agreement'
  parent: system.admin_config_system
  weight: 100

auctions.auction_settings:
  title: Auction settings
  parent: system.admin_config_system
  route_name: auctions.auction_settings
  weight: 10
