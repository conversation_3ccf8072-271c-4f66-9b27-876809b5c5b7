/**
 * @file
 * Auctions behaviors.
 */

(function ($, Drupal, drupalSettings) {

  'use strict';

  if (jQuery('#felling-certificate-tooltip').length > 0 || jQuery('#felling-certificate-owner-tooltip').length > 0) {
    const htmlDesc = `
      <div id="felling-certificate-tooltip-text" class="form-item__description formtips-processed" style="max-width: 500px;">
        Ciršanas apliecinājuma kopijas dokuments būs redzams tikai cirsmas uzvarētājam, lai varētu sagatavot cirsmas pirkuma līguma dokumentāciju.
      </div>
    `;
    const targetObject = jQuery('#felling-certificate-tooltip').length > 0 ? jQuery('#felling-certificate-tooltip') : jQuery('#felling-certificate-owner-tooltip').parent();

    targetObject.find('h2').append('<a class="formtip"></a>');
    targetObject.find('h2').after(htmlDesc);

    jQuery(window).click(function() {
      if (jQuery('#felling-certificate-tooltip-text').hasClass('formtips-show')) {
        jQuery('#felling-certificate-tooltip-text').removeClass('formtips-show');
      }
    });

    targetObject.find('h2 a').click(function(e) {
      e.preventDefault();
      e.stopPropagation();

      jQuery('#felling-certificate-tooltip-text').addClass('formtips-show');
    });
  }

})(jQuery, Drupal, drupalSettings);
