/**
 * @file
 * Auctions behaviors.
 */

(function (Drupal) {

  'use strict';

  Drupal.behaviors.numberInputAlter = {
    setTwoNumberDecimal: function (element) {
      this.value =parseFloat(this.value).toFixed(2);
      return true;
    },
    attach: function () {
      const inputs = document.querySelectorAll('input[type="number"].round-100, .round-100 input[type="number"]');
      [...inputs].forEach((element) => {
        element.value = (Math.round(parseFloat(element.value)/100)*100).toFixed(2)
        element.addEventListener('change', Drupal.behaviors.numberInputAlter.setTwoNumberDecimal)
      });
    }
  };

} (Drupal));
