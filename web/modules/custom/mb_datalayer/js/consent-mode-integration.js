/**
 * @file
 * Integrates EU Cookie Compliance with Google Tag consent mode.
 */

(function ($, Drupal, drupalSettings, once) {
  'use strict';

  /**
   * Updates Google Tag consent settings based on EU Cookie Compliance choices.
   */
  Drupal.behaviors.mbDataLayerConsentIntegration = {
    attach: function (context, settings) {
      // Only run once
      once('mb-consent-integration', 'body', context).forEach(function () {
        // Check if both modules are available
        if (typeof Drupal.eu_cookie_compliance !== 'undefined' && typeof gtag === 'function') {

          // Function to update Google consent based on cookie compliance status
          var updateGoogleConsent = function(cookieStatus) {
            // Default - everything denied
            var consentSettings = {
              ad_storage: 'denied',
              analytics_storage: 'denied',
              ad_user_data: 'denied',
              ad_personalization: 'denied'
            };

            // Use the built-in hasAgreed() function to check consent status
            // This is more reliable than checking status codes directly
            var hasAgreed = Drupal.eu_cookie_compliance.hasAgreed();

            if (hasAgreed) {
              consentSettings = {
                ad_storage: 'granted',
                analytics_storage: 'granted',
                ad_user_data: 'granted',
                ad_personalization: 'granted'
              };
            }

            // Update Google consent settings
            gtag('consent', 'update', consentSettings);
          };

          // Check initial cookie status
          var currentStatus = Drupal.eu_cookie_compliance.getCurrentStatus();
          updateGoogleConsent(currentStatus);

          // Listen for changes in cookie compliance status
          $(document).on('eu_cookie_compliance.changeStatus', function(event, status) {
            updateGoogleConsent(status);
          });

          // Also handle the agree button click event
          $(document).on('click', '.agree-button', function() {
            setTimeout(function() {
              var updatedStatus = Drupal.eu_cookie_compliance.getCurrentStatus();
              updateGoogleConsent(updatedStatus);
            }, 100);
          });

          // Handle the disagree/decline button click event
          $(document).on('click', '.disagree-button, .decline-button', function() {
            setTimeout(function() {
              updateGoogleConsent(0); // 0 means disagreed
            }, 100);
          });
        }
      });
    }
  };

})(jQuery, Drupal, drupalSettings, once);
