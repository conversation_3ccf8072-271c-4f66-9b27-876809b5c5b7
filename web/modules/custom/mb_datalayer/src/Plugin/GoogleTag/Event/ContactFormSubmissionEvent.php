<?php

declare(strict_types=1);

namespace Drupal\mb_datalayer\Plugin\GoogleTag\Event;

use Dr<PERSON>al\google_tag\Plugin\GoogleTag\Event\EventBase;

/**
 * Contact form submission event plugin.
 *
 * @GoogleTagEvent(
 *   id = "contact_form_submission",
 *   event_name = "contact_form_submission",
 *   label = @Translation("Contact Form Submission"),
 *   description = @Translation("This event captures contact form submissions."),
 *   context_definitions = {
 *     "page" = @ContextDefinition("string"),
 *     "role" = @ContextDefinition("string"),
 *     "made_calculation" = @ContextDefinition("boolean"),
 *   }
 * )
 */
final class ContactFormSubmissionEvent extends EventBase {

  /**
   * {@inheritDoc}
   */
  public function getData(): array {
    return [
      'page' => $this->getContextValue('page'),
      'role' => $this->getContextValue('role'),
      'made_calculation' => $this->getContextValue('made_calculation'),
    ];
  }

}
