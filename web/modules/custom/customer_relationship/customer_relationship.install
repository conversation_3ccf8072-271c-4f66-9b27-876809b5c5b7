<?php

/**
 * @file
 * Install, update, and uninstall functions for customer_relationship module.
 */

use Drupal\customer_relationship\Entity\Customer;
use Drupal\user\Entity\User;

/**
 * Implements hook_install().
 */
function customer_relationship_install($is_syncing) {
  foreach (User::loadMultiple() as $user) {
    $customer = Customer::create([
      'uid' => $user->id(),
      'changed' => $user->getChangedTime(),
    ]);
    if (!$user->get('field_comments')->isEmpty()) {
      $customer->set('description', $user->get('field_comments')->getValue());
    }
    $customer->save();
  }
}

/**
 * Implements hook_uninstall().
 */
function customer_relationship_uninstall() {
  \Drupal::entityTypeManager()
    ->getStorage('customer')
    ->delete(Customer::loadMultiple());
}

/**
 * Implements hook_update_N().
 *
 * Update the customer entity type to include the tags field.
 */
function customer_relationship_update_9001() {
  foreach (Customer::loadMultiple() as $customer) {
    $user = $customer->getOwner();
    if (!$user) {
      $customer->delete();
      continue;
    }
    $customer->set('field_tags', $user->get('field_tags')->getValue());
    $customer->save();
  }
}
