{% set layout_modifiers = [] %}

{% set main_base_class = main_base_class|default('main') %}
{% set main_modifiers = [] %}

{% set main_content_base_class = main_content_base_class|default('main-content') %}
{% set main_content_modifiers = [] %}

{% set page_info = page.info %}
{% set page_navigation = page.navigation %}
{% set page_header = page.header %}

<div>

  {% include "@mb/partials/site-header.twig" with {
    'header_content': page_header,
    'navigation_content': page_navigation,
    'info_content': page_info
  }
  %}

  {% block page_system %}
    {{ page.breadcrumb }}
    {{ page.highlighted }}
    {{ page.help }}
    {{ messages }}
  {% endblock %}
  <div {{ bem(main_base_class, main_modifiers, main_blockname) }}>
    <a href="#main-content" id="main-content" tabindex="-1" aria-label="{{ 'Main content'|t }}"></a>{# link is in html.html.twig #}
    {% block page_hero %}
      {% if page.hero %}
        {{ page.hero }}
      {% endif %}
    {% endblock %}
    <div class="main-layout-wrapper">
      <main role="main" {{ bem(main_content_base_class, main_content_modifiers, main_content_blockname) }}>
        {% block page_content %}
          {{ page.content }}
        {% endblock %}
      </main>
      {% if page.sidebar %}
        <aside class="main-sidebar" role="complementary">
          {% block page_sidebar %}
            {{ page.sidebar }}
          {% endblock %}
        </aside>
      {% endif %}
    </div>
  </div>
  {% block page_footer %}
    {% if page.footer_left or page.footer_middle or page.footer_right %}
      <div class="footer footer--columns">
        <div class="footer__inner">
          {% if page.footer_left %}
            <div class="footer-left">
              {{ page.footer_left }}
            </div>
          {% endif %}
          {% if page.footer_second %}
            <div class="footer-second">
              {{ page.footer_second }}
            </div>
          {% endif %}
          {% if page.footer_middle %}
            <div class="footer-middle">
              {{ page.footer_middle }}
            </div>
          {% endif %}
          {% if page.footer_right %}
            <div class="footer-right">
              {{ page.footer_right }}
            </div>
          {% endif %}
        </div>
      </div>
      {{ page.footer }}
    {% endif %}
    {% if page.footer %}
      {{ page.footer }}
    {% endif %}
  {% endblock %}
</div>
