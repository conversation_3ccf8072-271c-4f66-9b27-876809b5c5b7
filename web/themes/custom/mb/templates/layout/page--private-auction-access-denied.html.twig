
{#
/**
 * @file
 * Theme override to display a single page.
 *
 * The doctype, html, head and body tags are not in this template. Instead they
 * can be found in the html.html.twig template in this directory.
 *
 * Available variables:
 *
 * General utility variables:
 * - base_path: The base URL path of the <PERSON><PERSON><PERSON> installation. Will usually be
 *   "/" unless you have installed <PERSON><PERSON><PERSON> in a sub-directory.
 * - is_front: A flag indicating if the current page is the front page.
 * - logged_in: A flag indicating if the user is registered and signed in.
 * - is_admin: A flag indicating if the user has permission to access
 *   administration pages.
 *
 * Site identity:
 * - front_page: The URL of the front page. Use this instead of base_path when
 *   linking to the front page. This includes the language domain or prefix.
 *
 * Page content (in order of occurrence in the default page.html.twig):
 * - messages: Status and error messages. Should be displayed prominently.
 * - node: Fully loaded node, if there is an automatically-loaded node
 *   associated with the page and the node ID is the second argument in the
 *   page's path (e.g. node/12345 and node/12345/revisions, but not
 *   comment/reply/12345).
 *
 * Regions:
 * - page.header: Items for the header region.
 * - page.primary_menu: Items for the primary menu region.
 * - page.secondary_menu: Items for the secondary menu region.
 * - page.highlighted: Items for the highlighted content region.
 * - page.help: Dynamic help text, mostly for admin pages.
 * - page.content: The main content of the current page.
 * - page.sidebar_first: Items for the first sidebar.
 * - page.sidebar_second: Items for the second sidebar.
 * - page.footer: Items for the footer region.
 * - page.breadcrumb: Items for the breadcrumb region.
 *
 * @see template_preprocess_page()
 * @see html.html.twig
 */
#}

{% extends "@mb/layout/page.html.twig" %}


{% block page_content %}
  <div class="card-white page-40x">
    <div class="content">
      <h2 class="page-title">{{ "Access denied"|t }}</h2>
      <p>{{ "This is a closed auction. Only invited bidders can view it."|t }}</p>
      <a href="{{ path('<front>') }}" class="button button--secondary">{{ "Back to all auctions"|t }}</a>
    </div>
    {% include "@mb/partials/responsive-image.twig" with {
      output_image_tag: true,
      image_src: '/' ~ directory ~ '/images/private-property.png',
      image_alt: 'Not found',
      responsive_image_blockname: 'not-found',
    } %}
  </div>
{% endblock %}
