{#
/**
 * @file
 * Theme override to display a single page.
 *
 * The doctype, html, head and body tags are not in this template. Instead they
 * can be found in the html.html.twig template in this directory.
 *
 * Available variables:
 *
 * General utility variables:
 * - base_path: The base URL path of the <PERSON><PERSON><PERSON> installation. Will usually be
 *   "/" unless you have installed <PERSON><PERSON><PERSON> in a sub-directory.
 * - is_front: A flag indicating if the current page is the front page.
 * - logged_in: A flag indicating if the user is registered and signed in.
 * - is_admin: A flag indicating if the user has permission to access
 *   administration pages.
 *
 * Site identity:
 * - front_page: The URL of the front page. Use this instead of base_path when
 *   linking to the front page. This includes the language domain or prefix.
 *
 * Page content (in order of occurrence in the default page.html.twig):
 * - messages: Status and error messages. Should be displayed prominently.
 * - node: Fully loaded node, if there is an automatically-loaded node
 *   associated with the page and the node ID is the second argument in the
 *   page's path (e.g. node/12345 and node/12345/revisions, but not
 *   comment/reply/12345).
 *
 * Regions:
 * - page.header: Items for the header region.
 * - page.primary_menu: Items for the primary menu region.
 * - page.secondary_menu: Items for the secondary menu region.
 * - page.highlighted: Items for the highlighted content region.
 * - page.help: Dynamic help text, mostly for admin pages.
 * - page.content: The main content of the current page.
 * - page.sidebar_first: Items for the first sidebar.
 * - page.sidebar_second: Items for the second sidebar.
 * - page.footer: Items for the footer region.
 * - page.breadcrumb: Items for the breadcrumb region.
 *
 * @see template_preprocess_page()
 * @see html.html.twig
 */
#}

{% set layout_modifiers = [] %}

{% set main_base_class = main_base_class|default('main') %}
{% set main_modifiers = [] %}

{% set main_content_base_class = main_content_base_class|default('main-content') %}
{% set main_content_modifiers = [] %}

{% set page_info = page.info %}
{% set page_navigation = page.navigation %}
{% set page_header = page.header %}

{% include "@mb/partials/site-header.twig" with {
  'header_content': page_header,
  'navigation_content': page_navigation,
  'info_content': page_info
}
%}

{% block page_system %}
  {{ page.breadcrumb }}
  {{ page.highlighted }}
  {{ page.help }}
  {{ messages }}
{% endblock %}

<div {{ bem(main_base_class, main_modifiers, main_blockname) }}>
  <a href="#main-content" id="main-content" tabindex="-1" aria-label="{{ 'Main content'|t }}"></a>{# link is in html.html.twig #}

  {% block page_hero %}
    {% if page.hero %}
      <div {{ bem('hero') }}>
        <div {{ bem('inner', [], hero__base_class) }}>
          {{ page.hero }}
        </div>
      </div>
    {% endif %}
  {% endblock %}

  <div class="main-layout-wrapper">
    <main role="main" {{ bem(main_content_base_class, main_content_modifiers, main_content_blockname) }}>
      {% block page_content %}
        {{ page.content }}
      {% endblock %}
    </main>
    {% if page.sidebar %}
      <aside class="main-sidebar" role="complementary">
        {% block page_sidebar %}
          {{ page.sidebar }}
        {% endblock %}
      </aside>
    {% endif %}
  </div>
</div>

{% block page_footer %}
  {% if page.footer_left or page.footer_middle or page.footer_right %}
    <div class="footer footer--columns">
      <div class="footer__inner">
        {% if page.footer_left %}
          <div class="footer-left">
            {{ page.footer_left }}
          </div>
        {% endif %}
        {% if page.footer_second %}
          <div class="footer-second">
            {{ page.footer_second }}
          </div>
        {% endif %}
        {% if page.footer_middle %}
          <div class="footer-middle">
            {{ page.footer_middle }}
          </div>
        {% endif %}
        {% if page.footer_right %}
          <div class="footer-right">
            {{ page.footer_right }}
          </div>
        {% endif %}
      </div>
    </div>
    {{ page.footer }}
  {% endif %}
  {% if page.footer %}
    {{ page.footer }}
  {% endif %}
{% endblock %}
