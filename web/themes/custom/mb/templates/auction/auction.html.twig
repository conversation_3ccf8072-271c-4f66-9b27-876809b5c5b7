{#
/**
 * @file
 * Default theme implementation to present an auction entity.
 *
 * This template is used when viewing a registered auction's page,
 * e.g., /admin/content/auction)/123. 123 being the auction's ID.
 *
 * Available variables:
 * - content: A list of content items. Use 'content' to print all content, or
 *   print a subset such as 'content.title'.
 * - attributes: HTML attributes for the container element.
 * - view_mode: View mode.
 *
 * @see template_preprocess_auction()
 */
#}
{%
  set classes = [
    'auction',
    'grid__item',
    view_mode,
    auction_status,
    user_status,
  ]
%}
{%
  set shared_fields = [
    'start_price',
    'current_price',
    'bids',
    'end_time',
    'remain_time'
  ]
%}
{%
  set propery_field = [
    'field_property_area_size',
    'field_property_forest_size',
    'field_total_forest_stock',
    'price_per_ha',
]
%}
{%
  set felling_fields = [
    'field_forest_volume',
    'field_species_composition',
    'field_cutting_area',
    'cubic_price',
  ]
%}
{%
  set custom_fields = [
    'field_quantity',
    'price_per_quantity',
  ]
%}
<article{{ attributes.addClass(classes) }}>
    {% if content.field_map_image.0 %}
        {{ content.field_map_image }}
    {% endif %}
    {% if content.title %}
      <div class="card__heading">
        {{ content.title }}
        {{ content.subtitle }}
      </div>
    {% endif %}
    {% if content %}
      <div class="card__inner">
        {% if property_auction %}
          {% for field in propery_field %}
            {{ content[field] }}
          {% endfor %}
        {% elseif felling_auction %}
          {% for field in felling_fields %}
            {{ content[field] }}
          {% endfor %}
        {% elseif custom_auction %}
          {% for field in custom_fields %}
            {{ content[field] }}
          {% endfor %}
        {% endif %}
        {% for field in shared_fields %}
          {{ content[field] }}
        {% endfor %}
      </div>
    {% endif %}
    <div class="card__footer">
      {{ content.user_auction_status }}
    </div>
</article>
