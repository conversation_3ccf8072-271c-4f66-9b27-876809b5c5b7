{#
/**
 * Available variables:
 * - li_base_class - the base classname
 * - li_modifiers - array of modifiers to add to the base classname
 * - li_blockname - blockname prepended to the base classname
 * - list_item_label - (optional) a <strong> label before the list item itself
 * - list_item_content - the content of the list_item (typically text)
 *
 * Available blocks:
 * - list_item_content - used to replace the content of the list_item with something other than text
 *   for example: to insert the image and/or link components
 */
#}
{% set li_base_class = li_base_class|default('list-item') %}

<li {{ bem(li_base_class, li_modifiers, li_blockname) }}>
  {% block list_item_content %}
    {% if list_item_label %}<strong>{{ list_item_label }}</strong> {% endif %}
    {{ list_item_content }}
  {% endblock %}
</li>
