{#
/**
 * Variable names are same as link's for compatibility.
 *
 * Available variables:
 * - link_content - the content of the link (typically text)
 * - link_url - the url this link should poing to
 * - link_attributes - array of attribute and value pairs (from <PERSON><PERSON><PERSON>)
 * - link_modifiers - array of modifiers to add to the base classname
 * - link_extra_classes - array of extra classes to append to the other classnames
 *
 * Available blocks:
 * - link_content - used to replace the content of the link
 *     Example: to insert the image component
 */
#}
{# @TODO Try using `action-link` instead of this, as they appear to be very similar. #}
{% set link_base_class = link_base_class|default('action-link') %}
{% set link_modifiers = link_modifiers|default([]) %}
{% set link_extra_classes = link_extra_classes|default([]) %}
{% set link_is_external = 'external' in link_modifiers %}

<a
  href="{{ link_url }}"
  {{ bem(link_base_class, link_modifiers, link_blockname, link_extra_classes) }}
  {% for attribute, value in link_attributes %}
    {{ attribute }}="{{ value }}"
  {% endfor %}
>
{% if link__icon %}
  {% include "@mb/partials/icon.twig" with {
    icon_name: link__icon,
    icon_blockname: link_base_class,
    icon_decorative: true,
  } %}
{% endif %}
{% block link_content %}
  <span class="link-content">
    {{ link_content }}
  </span>
{% endblock %}

  {% if link_is_external %}
    <span class="visually-hidden">({{ "link to a different website"|t }})</span>
  {% endif %}
</a>
