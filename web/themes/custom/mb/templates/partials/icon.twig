{#
/**
 * Available variables:
 * - icon_base_class - base class name
 * - icon_modifiers - modifiers for icons (array)
 * - icon_blockname - blockname prepended to the base classname
 * - icon_name - the name of the icon
 * - icon_role - a11y role
 * - icon_title - a11y title
 * - icon_desc - a11y description
 * - icon_decorative - boolean to indicate if the icon is decorative
 * - icon_tabbable - boolean to indicate if the icon is tabbable
 */
#}
{% set icon_base_class = icon_base_class|default('icon') %}
{# If `directory` is defined, set the path relative for <PERSON><PERSON><PERSON>.
 # Otherwise, set the path relative to the Component Library. #}
{% set icon_url = directory ? '/' ~ directory ~ '/dist/' %}


<svg {{ bem(icon_base_class, icon_modifiers, icon_blockname, icon_extra_class) }}

  {% if icon_decorative %}
    aria-hidden="true" role="img"
  {% elseif icon_role %}
    role="{{ icon_role }}"
  {% endif %}

  {% if icon_title %}
    aria-labelledby="title-{{ icon_name }}"
  {% endif %}

  {% if icon_desc %}
    aria-describedby="desc-{{ icon_name }}"
  {% endif %}

  {% if icon_tabbable %}
    tabindex="0"
  {% endif %}
>
  {% if icon_title %}
    <title id="title-{{ icon_name }}">{{ icon_title }}</title>
  {% endif %}
  {% if icon_desc %}
    <desc id="desc-{{ icon_name }}">{{ icon_desc }}</desc>
  {% endif %}
  <use xlink:href="{{ icon_url }}icons.svg#{{ icon_name }}"></use>
</svg>
