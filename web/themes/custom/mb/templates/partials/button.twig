{#
/**
 * Available variables:
 * - button__content - the content of the button (typically text)
 *
 * Available blocks:
 * - button__content - used to replace the content of the button with something other than text
 *   for example: to insert an icon
 */
#}

{% set button__base_class = button__base_class|default('button') %}
{% set button__modifiers = button__modifiers|default([]) %}
{% set button__extra_classes = button__extra_classes|default([]) %}

{% if button__icon_only %}
  {% set button__modifiers = button__modifiers|merge(['icon-only']) %}
{% endif %}

{% set button__attributes = button__attributes|default({ class: [] }) %}
{# We merge classes array from `button__attributes` with `button__extra_classes` array #}
{% set button__attributes = button__attributes|merge({
  'class': bem(button__base_class, button__modifiers, button__blockname, button__extra_classes|merge(button__attributes.class)),
}) %}

<button {{ add_attributes(button__attributes) }} >
  {% if button__icon %}
    {% include "@mb/partials/icon.twig" with {
      icon_name: button__icon,
      icon_blockname: button__base_class,
      icon_decorative: true,
    } %}
  {% endif %}
  {% if button__icon_hidden %}
    {% include "@mb/partials/icon.twig" with {
      icon_name: button__icon_hidden,
      icon_blockname: button__base_class,
      icon_modifiers: ['hidden'],
      icon_decorative: true,
    } %}
  {% endif %}
  {% block button_content %}
    {% if button__icon_only %}
      <span class="visually-hidden">
    {% endif %}

    {% if button__expanded_content %}
      <span {{ bem('expandable-text', [], button__base_class) }}>
        <span {{ bem('collapsed-text', [], button__base_class) }}>
          {{ button__content }}
        </span>
        <span {{ bem('expanded-text', [], button__base_class) }}>
          {{ button__expanded_content }}
        </span>
      </span>
    {% else %}
      {{ button__content }}
    {% endif %}

    {% if button__icon_only %}
      </span>
    {% endif %}
  {% endblock %}
</button>
