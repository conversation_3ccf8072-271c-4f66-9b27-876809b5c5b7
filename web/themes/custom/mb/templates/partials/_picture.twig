{% set picture_base_class = picture_base_class|default('picture') %}

<picture
  {{ bem(picture_base_class, picture_modifiers, picture_blockname) }}
>
  {% if sources %}
    {#
    Internet Explorer 9 doesn't recognise source elements that are wrapped in
    picture tags. See http://scottjehl.github.io/picturefill/#ie9
    #}
    <!--[if IE 9]><video style="display: none;"><![endif]-->
    {% for source_attributes in sources %}
      <source{{ source_attributes }}/>
    {% endfor %}
    <!--[if IE 9]></video><![endif]-->
  {% endif %}
  {# The controlling image, with the fallback image in srcset. #}
  {% include "@mb/partials/_image.twig" with {
    image_blockname: picture_image_blockname|default(picture_blockname),
  } %}
</picture>
