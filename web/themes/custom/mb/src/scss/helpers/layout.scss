@import '~breakpoint-sass/stylesheets/breakpoint';

$space: 1rem; // 16px

@function space($multiplier: 1) {
  @return $space * $multiplier;
}

$space-double: space(2);
$page-space: $space-double;

$width-content: space(60);
$width-content--hero: space(65);
$width-content--image-hero: space(100);
$width-content--image: space(85);
$width-page: space(130);
$width-gutter: space(1);
$width-backdrop: space(120);

$width-xs: space(25); // 320
$width-s: space(45); // 720
$width-m: space(60); // 960
$width-l: space(80); // 1280
$width-xl: $width-page; // space(140) or 1600px

$width-bar-menu: $width-m;
$outer-container-break: $width-s;

$height-card-image: space(14);
$space-elements: space(4);

$space-page-horizontal-xs: space(2);
$space-page-horizontal-s: space(3);

$space-sidebar: space(13);

@mixin container($width: $width-content--image) {
  width: $width;
  max-width: min($width, 98vw);
  margin-left: 50%;
  transform: translateX(-50%);
  margin-bottom: space(2);
}

// Mixin for breakpoints
// Allows easier @include syntax
@mixin from-xs {
  @include breakpoint($width-xs) {
    @content;
  }
}
@mixin from-s {
  @include breakpoint($width-s) {
    @content;
  }
}
@mixin from-m {
  @include breakpoint($width-m) {
    @content;
  }
}
@mixin from-l {
  @include breakpoint($width-l) {
    @content;
  }
}
@mixin from-xl {
  @include breakpoint($width-xl) {
    @content;
  }
}

@mixin with-bar-menu {
  @include breakpoint($width-bar-menu) {
    @content;
  }
}

/**
 * Mixin - Between
 * Target viewports in a range from given breakpoint $smaller
 * to breakpoint $larger.
 */
@mixin between($smaller, $larger) {
  @media (min-width: $smaller) and (max-width: #{$larger - space(0.01)}) {
    @content;
  }
}

/**
 * Mixin - Below
 * Target viewports from 0 to given breakpoint $breakpoint.
 * The inverse behavior of mobile first.
 */
@mixin below($breakpoint) {
  @media (max-width: #{$breakpoint - space(0.01)}) {
    @content;
  }
}

/**
 * Mixin - Above
 * Target viewports from a given breakpoint to infinity.
 */
@mixin above($breakpoint) {
  @media (min-width: $breakpoint) {
    @content;
  }
}

/*
 * Mixin - Wrapper-width
 * Restrict container width.
 */
@mixin wrapper-width(
  $width: $width-page,
  $margin: 0
) {
  max-width: $width;
  width: 100%;
  margin: #{$margin} auto;
}

/*
 * Mixin - Wrapper-gutter
 * Add horizontal gutter to outer containers.
 */
@mixin wrapper-gutter($narrower-gutter: false) {
  @if $narrower-gutter == false {
    padding-left: $width-gutter;
    padding-right: $width-gutter;
    @include from-s {
      padding-left: $width-gutter + space(1);
      padding-right: $width-gutter + space(1);
    }
  } @else {
    padding-left: calc($width-gutter / 2);
    padding-right: calc($width-gutter / 2);
    @include from-s {
      padding-left: calc($width-gutter / 2 + space(1));
      padding-right: calc($width-gutter / 2 + space(1));
    }
  }
}

/*
 * Mixin - Wrapper
 * Outer container mixin.
 */
@mixin wrapper {
  @include wrapper-width;
  @include wrapper-gutter;
}


/**
 * Mixin - Break-out-of-wrapper
 * Enables nested content to be wider than it's parent.
 */
@mixin break-out-of-wrapper(
  $v-margin: 0,
  $h-margin: auto,
  $width: $width-backdrop
) {
  position: relative;
  width: $width;
  max-width: 100vw;
  left: 50%;
  transform: translateX(-50%);
  margin: #{$v-margin} #{$h-margin};
}

@mixin clearfix {
  &::after {
    clear: both;
    content: '';
    display: table;
  }
}

@mixin margin {
  margin-bottom: 1em;
}

@mixin no-bottom {
  margin-bottom: 0;
}

.hide-element {
  border: 0;
  clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px !important;
}

/*
 * Use this on the outer wrapper of page-level elements.
 * It ensures consistent spacing between elements on the page.
 */
@mixin space-stack-page {
  margin-bottom: space(2);
}

@mixin in-ie11 {
  @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
    @content;
  }
}
