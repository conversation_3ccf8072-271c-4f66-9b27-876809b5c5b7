.all-auction-summary {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .col-left,
  .col-center,
  .col-right {
    width: 100%;
    @include from-s {
      width: 49%;
    }
    @include from-m {
      width: 32%;
    }
  }
  .col-center {
    @include from-s {
      order: 3;
    }
    @include from-m {
      order: initial;
    }
  }
  .field {
    margin: 0;
    font-size: 1.4rem;
    border-bottom: 1px solid $color--gray;

    .inner {
      height: 100%;
      padding: space(0.25) 0;
      display: flex;
      flex-flow: row wrap;

      .field__label {
        flex: 1 0 30%;
        font-weight: 500;
      }
      .field__item {
        padding-left: space();
        flex: none;
        font-weight: normal;
      }
    }
  }
}
