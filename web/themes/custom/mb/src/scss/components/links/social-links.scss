.social-links__list {
  @include list-reset;

  padding: 0;
  display: flex;
  flex-flow: column nowrap;
  .social-links__item {
    .social-links__link {
      display: flex;
      align-items: center;
      padding-top: space(1);
      padding-bottom: space(1);
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
      svg {
        height: 2em;
        width: 2em;
        margin-right: 1em;
        fill: $color--default-text-negative;
      }
    }
  }
}
