
.form-item--checkboxes,
.form-item--checkbox__item {
  @include list-reset;
}

$md-checkbox-checked-color: $color--form-el-checked;
$md-checkbox-border-color: $color--form-el-border;
$md-checkbox-border-color-disabled: #999;
$md-checkbox-checked-color-disabled: #999;

$md-checkbox-margin: 1rem 0;
$md-checkbox-size: 2.2rem;
$md-checkbox-padding: 0.5rem;
$md-checkbox-border-width: 2px;
$md-checkbox-border-radius: 2px;
$md-checkmark-width: 2px;
$md-checkmark-color: #fff;
$md-checkbox-label-padding: 1.5rem;

.js-form-type-checkbox,
.md-checkbox {
  position: relative;
  margin: $md-checkbox-margin;
  text-align: left;

  &.md-checkbox-inline {
    display: inline-block;
  }

  label {
    cursor: pointer;
    display: inline;
    line-height: $md-checkbox-size;
    vertical-align: top;
    clear: both;
    padding-left: 1px;
    &:not(:empty) {
      padding-left: $md-checkbox-label-padding;
    }

    &::before, &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
    }

    &::before {
      // box
      width: $md-checkbox-size;
      height: $md-checkbox-size;
      background: #fff;
      border: $md-checkbox-border-width solid $md-checkbox-border-color;
      border-radius: $md-checkbox-border-radius;
      cursor: pointer;
      transition: background .3s;
    }

    &::after {
      // checkmark
    }
  }

  input[type="checkbox"] {
    outline: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: .8em;
    &:focus + label::before {
      border: $md-checkbox-border-width solid $md-checkbox-checked-color;
    }
    &:checked {
      + label::before{
        background: $md-checkbox-checked-color;
        border:none;
      }
      + label::after {

        $md-checkmark-size: $md-checkbox-size - 2 * $md-checkbox-padding;

        transform: translate($md-checkbox-padding, calc(($md-checkbox-size / 2) - ($md-checkmark-size / 2.6))) rotate(-45deg);
        width: $md-checkmark-size;
        height: calc($md-checkmark-size / 2);
        border: $md-checkmark-width solid $md-checkmark-color;
        border-top-style: none;
        border-right-style: none;
      }
    }

    &:disabled {
      + label::before{
        border-color: $md-checkbox-border-color-disabled;
      }
      &:checked {
        + label::before{
          background: $md-checkbox-checked-color-disabled;
        }
      }
    }
  }
}
