.form-item--radios,
.form-item--radio__item {
  @include list-reset;
}

.form-item--radio {
  .form-item {
    &.js-form-type-radio {
      margin: .5em 0;
    }
  }
}

$md-radio-checked-color: $color--form-el-checked;
$md-radio-border-color: $color--form-el-border;
$md-radio-size: 22px;
$md-radio-checked-size: 12px;
$md-radio-ripple-size: 5px;

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.0);
  }
  50% {
    box-shadow: 0 0 0 $md-radio-ripple-size rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow: 0 0 0 $md-radio-ripple-size rgba(0, 0, 0, 0);
  }
}

.js-form-type-radio {

  &.md-radio-inline {
    display: inline-block;
  }

  input[type="radio"] {
    @extend .hide-element;

    &:checked + label::before {
      border-color: $md-radio-checked-color;
      animation: ripple 0.2s linear forwards;
    }
    &:checked + label::after {
      transform: scale(1);
    }
    &:focus + label::after {
      background: $md-radio-checked-color;
    }
  }

  label:not(.rating-label) {
    display: inline-block;
    position: relative;
    padding: 0 0 0 ($md-radio-size + 10px);
    margin-bottom: 0;
    cursor: pointer;
    vertical-align: bottom;
    &::before, &::after {
      position: absolute;
      content: '';
      border-radius: 50%;
      transition: all .3s ease;
      transition-property: transform, border-color;
    }
    &::before {
      left: 0;
      top: 0;
      width: $md-radio-size;
      height: $md-radio-size;
      border: 2px solid $md-radio-border-color;
      background-color: white;
    }
    &::after {
      top: calc($md-radio-size / 2 - $md-radio-checked-size / 2);
      left: calc($md-radio-size / 2 - $md-radio-checked-size / 2);
      width:$md-radio-checked-size;
      height:$md-radio-checked-size;
      transform: scale(0);
      background:$md-radio-checked-color;
    }
  }
}
