.mb-charts-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
  justify-items: center;
  grid-auto-columns: 1fr;
  @include from-s {
    grid-template-columns: repeat(2, 1fr);
  }
  @include from-l {
    grid-template-columns: repeat(4, 1fr);
  }

  .mb-charts-info-shield {
    @extend .card-white;
    display: grid;
    gap: 1rem;
    // Take up full grid row.
    grid-column: span 4;
    grid-template-columns: 1fr;
    width: 100%;
    @include from-s {
      grid-template-columns: 2fr 3fr;
      grid-column: span 2;
    }
    @include from-l {
      grid-column: span 1;
    }
    .mb-charts-info-shield-icon {
      display: flex;
      align-items: center;
      lord-icon {
        height: 100px;
        width: 100%;
      }
    }
    .mb-charts-info-shield-content {
      display: grid;
      gap: 1rem;
      justify-items: center;
      text-align: center;
      .shield-title {
        font-size: 1.5rem;
        font-weight: 700;
      }
      .shield-value {
        font-size: 2rem;
        font-weight: 700;
        color: $color--primary;
      }
      .shield-description {
        font-size: 1.35rem;
        font-weight: 400;
      }
    }
  }

  .chart-wrapper {
    @extend .card-white;
    grid-column: span 4;
    width: 100%;
    @include from-s {
      grid-column: span 2;
    }

    .chart {
      width: 100% !important;
      height: 100% !important;
    }
  }

  .mb-charts-grid-1 {
    grid-column: span 1;
    width: 100%;
  }

  .mb-charts-grid-2 {
    grid-column: span 2;
    width: 100%;
  }

  .mb-charts-grid-3 {
    grid-column: span 3;
    width: 100%;
  }

  .mb-charts-grid-4 {
    grid-column: span 4;
    width: 100%;
  }

  img.leaflet-marker-icon.leaflet-zoom-animated.leaflet-interactive {
    border-radius: 50%;
    border: 2px solid white;
  }

}
