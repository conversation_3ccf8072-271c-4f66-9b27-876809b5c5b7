.auction.teaser,
.auction.search_limited,
.auction.map_teaser {
  @extend .shadow-md;
  margin-bottom: 1rem;
  background-color: white;
  overflow: hidden;
  border-radius: 3px;
  &.pending {
    background: repeating-linear-gradient(
        45deg,
        $color--status-background,
        $color--status-background 30px,
        $color--secondary-action 30px,
        $color--secondary-action 60px
    );
  }
  &.declined,
  &.inactive {
    background: repeating-linear-gradient(
        45deg,
        $color--secondary-action,
        $color--secondary-action 30px,
        white 30px,
        white 60px
    );
  }
  &.finished {
    background-color: $color--meta-background;
    .user-auction-status::after {
      content: '';
      display: inline-block;
      background: url(../images/ended.svg) center center / contain no-repeat;
      width: 1.4em;
      height: 1.5em;
      position: absolute;
      right: 0;
    }
  }

  &.canceled {
    background-color: $color--meta-background;
    .user-auction-status::after {
      content: '';
      display: inline-block;
      background: url(../images/canceled.svg) center center / contain no-repeat;
      width: 1.4em;
      height: 1.5em;
      position: absolute;
      right: 0;
    }
  }
  .card__heading {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-content: baseline;
    padding: 1rem 1rem 0;
    will-change: transform;
  }
  .card__inner {
    padding: 1rem 2rem;
    width: 100%;
    will-change: transform;
  }
  .card__footer {
    width: 100%;
    padding: 0 2rem 1rem;
    display: flex;
    justify-content: center;
    min-height: 4rem;
    will-change: transform;
  }
  .auction-title, .auction-subtitle {
    flex: 1 0 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
  }
  .auction-title {
    a {
      display: inline-block;
      color: $color--primary-line;
      text-decoration: none;
      text-align: center;
      &:hover {
        color: $color--primary-text;
      }
    }
  }
  .auction-subtitle {
    a {
      display: inline-block;
      color: $color--default-text;
      text-decoration: none;
      text-align: center;
      &:hover {
        color: $color--primary-text;
      }
    }
  }
  &.active {
    &.final-countdown {
      position: relative;
      z-index: 0;
      height: auto;
      overflow: hidden;
      box-shadow: none;
      picture {
        padding: 1px;
        border-top-left-radius: 3px;
        border-top-right-radius: 3px;
      }
      img {
        border-top-left-radius: 3px;
        border-top-right-radius: 3px;
      }
      &::before {
        content: "";
        position: absolute;
        z-index: -2;
        left: -50%;
        top: -50%;
        width: 200%;
        height: 200%;
        background-color: $color--primary-line;
        background-repeat: no-repeat;
        background-size: 50% 50%, 50% 50%;
        background-position: 0 0, 100% 0, 100% 100%, 0 100%;
        background-image: linear-gradient($color--primary-line, $color--default-background);
        -webkit-animation: countdown 4s linear infinite;
        animation: countdown 4s linear infinite;
      }
      &::after {
        content: "";
        position: absolute;
        z-index: -1;
        left: 1px;
        top: 1px;
        width: calc(100% - 2px);
        height: calc(100% - 2px);
        background: white;
        border-radius: 3px;
      }
      .field--name-end-time .countdown {
        transition: all 2s;
        font-size: 1.5rem;
        line-height: 1.5rem;
        animation-name: countdown-font;
        animation-duration: 1s;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
        animation-play-state: running;
      }
    }
    &.ends-soon {
      .user-auction-status::after {
        content: '';
        display: inline-block;
        background: url(../images/timer.svg) center center / contain no-repeat;
        width: 1.4em;
        height: 1.5em;
        position: absolute;
        right: 0;
      }
    }
    .countdown {
      font-weight: 500;
      color: $color--primary-line;
    }
    &.private {
      .auction-subtitle::after {
        content: '';
        display: inline-block;
        background: url(../images/private.svg) center center / contain no-repeat;
        width: 1.4em;
        height: 1.5em;
        margin-left: 0.5em;
      }
    }
  }
  .field {
    flex: 1 0 100%;
    margin: 0;
    font-size: 1.4rem;
    border-bottom: 1px solid $color--gray;

    .inner {
      height: 100%;
      padding: space(0.25) 0;
      display: flex;
      flex-flow: row wrap;

      .field__label {
        flex: 1 0 45%;
        font-weight: 500;
      }
      .field__item {
        padding-left: space();
        font-weight: normal;
      }
    }
  }
  .field--name-field-map-image {
    padding: 0;
    border: 0;
    will-change: transform;
    img {
      width: 100%;
      height: auto;
    }
  }

  .user-auction-status {
    display: flex;
    margin: 0;
    font-weight: 500;
    text-align: center;
    justify-content: center;
    align-items: center;
    position: relative;
    min-width: 100%;
    padding: 0 2em;
  }
  &.status-danger {
    .user-auction-status {
      color: $color--error;
    }
  }
  &.status-success {
    .user-auction-status {
      color: $color--primary-line;
    }
  }
}

// Leaflet popup
.leaflet-popup-content {
  width: 200px !important;
  .auction.teaser,
  .auction.map_teaser {
    width: 100%;
    margin: 0;
    background-color: white;
    box-shadow: none;
    .auction-title {
      font-size: medium;
    }
    .auction-subtitle {
      font-size: small;
    }
    .card {
      &__heading,
      &__inner,
      &__footer {
        padding: 0;
        min-height: 0;
      }
    }
    .field {
      font-size: 1.3rem;
    }
  }
}

@keyframes countdown {
  100% {
    transform: rotate(1turn);
  }
}

@keyframes countdown-font {
  0% {
    color: $color--primary-line;
    font-size: 1.4rem;
  }
  50% {
    color: rgba(3, 126, 13, 0.8);
    font-size: 1.45rem;
  }
  100% {
    color: $color--primary-line;
    font-size: 1.4rem;
  }
}
