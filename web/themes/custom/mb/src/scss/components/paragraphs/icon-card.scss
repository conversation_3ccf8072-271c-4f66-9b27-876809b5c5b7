.paragraph-icon-card {
  @extend .card-white;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: default;
  .field--name-field-image {
    display: flex;
    justify-content: center;
    max-width: 100px;
  }
  lord-icon {
    height: 100px;
    width: 100%;
  }

  .text-long {
    margin-top: 1rem;
  }
}

a.paragraph-icon-card {
  cursor: pointer;
  text-decoration: none;
  color: $color--darkest;
  :hover {
    color: $color--gray-dark;
  }
}
