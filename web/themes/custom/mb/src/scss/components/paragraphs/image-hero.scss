.paragraph-image-hero {
  position: relative;
  min-height: 40rem; // Ensure enough space for text
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .dark-overlay-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }

  .hero-image__title {
    @include wrapper-width;
    @include wrapper-gutter;
    position: relative; // Change from absolute to relative
    z-index: 2;
    color: white;
    text-align: center;
    width: 90%;
    max-width: 1200px;
    padding: 2rem;

    a:not(.button) {
      color: white;
    }

    font-size: 2rem;
    @include from-xs {
      font-size: 3rem;
    }
    @include from-s {
      font-size: 4rem;
    }
    @include from-m {
      font-size: 5rem;
    }

    .text-long {
      padding: 3rem 2rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      line-height: 1.4;

      p {
        margin: 0;
        text-align: center;
      }
    }
  }

  .field--name-field-image {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
