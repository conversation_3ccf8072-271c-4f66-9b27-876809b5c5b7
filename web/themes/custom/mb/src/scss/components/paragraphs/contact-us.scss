.width-wide {
  .paragraph-contact-us:not(.paragraph-banner *) {
    @include wrapper-width;
    @include wrapper-gutter;
  }
}

.paragraph-contact-us {
  @extend .card-white;
  min-height: 40rem;
  form {
    margin: 0;
  }
  input[type=submit] {
    margin-top: 0;
  }
  input:not([type="submit"]) {
    width: 100%;
  }
  textarea {
    resize: none;
    border: $form-el-border-size solid $color--form-el-border;
    width: 100%;
    border-radius: 3px;
    padding: 0.5em;
  }
  .sent-msg {
    @extend .h2;
    height: 100%;
    align-content: center;
    align-items: center;
    display: flex;
    vertical-align: bottom;
    text-align: center;
  }
}
