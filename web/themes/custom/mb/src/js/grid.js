/**
 * @file
 * Grid behaviour.
 */

(function (<PERSON><PERSON><PERSON>, $) {
  Drupal.behaviors.gridToggle = {
    attach: function () {
      $('.grid--show-more').each(function() {
        // Add event listener for .button--action.
        $(this).find('.grid__controls .button').click(function(e) {
          // Toggle the .grid--show-more--expanded class.
          e.preventDefault()
          $(this).parent().parent().find('.grid__items--hidden').removeClass('grid__items--hidden');
          $(this).parent().hide();
        });
      });
    }
  };

} (<PERSON><PERSON><PERSON>, jQuery));
